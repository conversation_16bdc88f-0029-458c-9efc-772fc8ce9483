'use client';

import { useAuth } from '@/hooks/useAuth';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import CandidateDashboard from '@/components/dashboard/CandidateDashboard';
import RecruiterDashboard from '@/components/dashboard/RecruiterDashboard';
import AdminDashboard from '@/components/dashboard/AdminDashboard';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function Dashboard() {
  const { user } = useAuth();

  const renderDashboardContent = () => {
    switch (user?.role) {
      case 'candidate':
        return <CandidateDashboard />;
      case 'recruiter':
        return <RecruiterDashboard />;
      case 'admin':
        return <AdminDashboard />;
      default:
        return <div>Loading...</div>;
    }
  };

  return (
    <ProtectedRoute>
      <DashboardLayout>
        {renderDashboardContent()}
      </DashboardLayout>
    </ProtectedRoute>
  );
}
