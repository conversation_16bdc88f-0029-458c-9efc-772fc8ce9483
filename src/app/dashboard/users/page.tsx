'use client';

import React, { useState } from 'react';
import { Plus } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import { UsersDataTable, User } from '@/components/dashboard/tables/UsersDataTable';
import { UserModal } from '@/components/dashboard/modals/UserModal';
import { UserDrawer } from '@/components/dashboard/drawers/UserDrawer';
import { AdminRoute } from '@/components/auth/ProtectedRoute';

// Mock data - replace with real API calls
const mockUsers: User[] = [
  {
    id: '1',
    userId: 'user_abc123',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    role: 'candidate',
    status: 'active',
    isEmailVerified: true,
    lastLoginAt: '2024-01-15T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    userId: 'user_def456',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    role: 'recruiter',
    status: 'active',
    isEmailVerified: true,
    lastLoginAt: '2024-01-14T15:45:00Z',
    createdAt: '2024-01-02T00:00:00Z',
  },
  {
    id: '3',
    userId: 'user_ghi789',
    firstName: 'Mike',
    lastName: 'Johnson',
    email: '<EMAIL>',
    role: 'candidate',
    status: 'pending',
    isEmailVerified: false,
    createdAt: '2024-01-10T00:00:00Z',
  },
  {
    id: '4',
    userId: 'user_jkl012',
    firstName: 'Sarah',
    lastName: 'Wilson',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    isEmailVerified: true,
    lastLoginAt: '2024-01-16T09:15:00Z',
    createdAt: '2023-12-15T00:00:00Z',
  },
];

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [isUserDrawerOpen, setIsUserDrawerOpen] = useState(false);

  const handleCreateUser = async (data: any) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newUser: User = {
        id: Date.now().toString(),
        userId: `user_${Math.random().toString(36).substr(2, 9)}`,
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        role: data.role,
        status: data.status,
        phone: data.phone,
        isEmailVerified: data.isEmailVerified,
        createdAt: new Date().toISOString(),
      };
      
      setUsers(prev => [newUser, ...prev]);
    } catch (error) {
      console.error('Error creating user:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditUser = async (data: any) => {
    if (!selectedUser) return;
    
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUsers(prev => prev.map(user => 
        user.id === selectedUser.id 
          ? { ...user, ...data }
          : user
      ));
    } catch (error) {
      console.error('Error updating user:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async (user: User) => {
    if (!confirm(`Are you sure you want to delete ${user.firstName} ${user.lastName}?`)) {
      return;
    }

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setUsers(prev => prev.filter(u => u.id !== user.id));
      setIsUserDrawerOpen(false);
    } catch (error) {
      console.error('Error deleting user:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusChange = async (user: User, newStatus: string) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setUsers(prev => prev.map(u => 
        u.id === user.id 
          ? { ...u, status: newStatus as any }
          : u
      ));
    } catch (error) {
      console.error('Error updating user status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setIsUserDrawerOpen(true);
  };

  const handleEditUserClick = (user: User) => {
    setSelectedUser(user);
    setIsUserModalOpen(true);
  };

  const handleCreateUserClick = () => {
    setSelectedUser(null);
    setIsUserModalOpen(true);
  };

  return (
    <AdminRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Users</h1>
              <p className="text-muted-foreground">
                Manage user accounts and permissions
              </p>
            </div>
            <Button onClick={handleCreateUserClick}>
              <Plus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{users.length}</div>
                <p className="text-xs text-muted-foreground">
                  +2 from last week
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {users.filter(u => u.status === 'active').length}
                </div>
                <p className="text-xs text-muted-foreground">
                  {Math.round((users.filter(u => u.status === 'active').length / users.length) * 100)}% of total
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Candidates</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {users.filter(u => u.role === 'candidate').length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Most common role
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Recruiters</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {users.filter(u => u.role === 'recruiter').length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Active recruiters
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle>All Users</CardTitle>
              <CardDescription>
                A list of all users in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UsersDataTable
                data={users}
                isLoading={isLoading}
                onView={handleViewUser}
                onEdit={handleEditUserClick}
                onDelete={handleDeleteUser}
                onStatusChange={handleStatusChange}
              />
            </CardContent>
          </Card>
        </div>

        {/* Modals and Drawers */}
        <UserModal
          open={isUserModalOpen}
          onOpenChange={setIsUserModalOpen}
          user={selectedUser}
          onSubmit={selectedUser ? handleEditUser : handleCreateUser}
          isLoading={isLoading}
        />

        <UserDrawer
          open={isUserDrawerOpen}
          onOpenChange={setIsUserDrawerOpen}
          user={selectedUser}
          onEdit={handleEditUserClick}
          onDelete={handleDeleteUser}
        />
      </DashboardLayout>
    </AdminRoute>
  );
}
