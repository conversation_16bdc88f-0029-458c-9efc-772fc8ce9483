'use client';

import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';

import LoginPage from '@/components/auth/LoginPage';

function LoginPageContent() {
  const searchParams = useSearchParams();
  const role = searchParams.get('role') as 'candidate' | 'recruiter' | 'admin' | null;

  return <LoginPage role={role} />;
}

export default function Login() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginPageContent />
    </Suspense>
  );
}
