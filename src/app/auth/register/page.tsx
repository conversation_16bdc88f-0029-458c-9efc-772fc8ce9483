'use client';

import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';

import RegisterPage from '@/components/auth/RegisterPage';

function RegisterPageContent() {
  const searchParams = useSearchParams();
  const role = searchParams.get('role') as 'candidate' | 'recruiter' | null;

  return <RegisterPage role={role} />;
}

export default function Register() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <RegisterPageContent />
    </Suspense>
  );
}
