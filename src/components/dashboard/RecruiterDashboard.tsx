'use client';

import React from 'react';
import { Briefcase, Users, Eye, TrendingUp, Plus, Calendar, FileText } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

export default function RecruiterDashboard() {
  // Mock data - replace with real API calls
  const stats = {
    activeJobs: 8,
    totalApplications: 156,
    interviewsScheduled: 12,
    hiredCandidates: 3,
  };

  const recentJobs = [
    {
      id: 1,
      title: 'Senior Frontend Developer',
      department: 'Engineering',
      status: 'active',
      applications: 23,
      views: 145,
      postedDate: '2024-01-10',
    },
    {
      id: 2,
      title: 'Product Manager',
      department: 'Product',
      status: 'active',
      applications: 18,
      views: 89,
      postedDate: '2024-01-08',
    },
    {
      id: 3,
      title: 'UX Designer',
      department: 'Design',
      status: 'draft',
      applications: 0,
      views: 0,
      postedDate: '2024-01-15',
    },
  ];

  const recentApplications = [
    {
      id: 1,
      candidateName: '<PERSON>',
      jobTitle: 'Senior Frontend Developer',
      status: 'interview_scheduled',
      appliedDate: '2024-01-14',
      experience: '5 years',
    },
    {
      id: 2,
      candidateName: 'Jane Smith',
      jobTitle: 'Product Manager',
      status: 'under_review',
      appliedDate: '2024-01-13',
      experience: '7 years',
    },
    {
      id: 3,
      candidateName: 'Mike Johnson',
      jobTitle: 'Senior Frontend Developer',
      status: 'shortlisted',
      appliedDate: '2024-01-12',
      experience: '4 years',
    },
  ];

  const upcomingInterviews = [
    {
      id: 1,
      candidateName: 'John Doe',
      jobTitle: 'Senior Frontend Developer',
      date: '2024-01-20',
      time: '10:00 AM',
      type: 'Technical Interview',
    },
    {
      id: 2,
      candidateName: 'Sarah Wilson',
      jobTitle: 'Product Manager',
      date: '2024-01-22',
      time: '2:00 PM',
      type: 'Final Interview',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'closed':
        return 'bg-red-100 text-red-800';
      case 'interview_scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'shortlisted':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'draft':
        return 'Draft';
      case 'closed':
        return 'Closed';
      case 'interview_scheduled':
        return 'Interview Scheduled';
      case 'under_review':
        return 'Under Review';
      case 'shortlisted':
        return 'Shortlisted';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Recruiter Dashboard</h1>
          <p className="text-gray-600">Manage your job postings and candidates.</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Post New Job
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeJobs}</div>
            <p className="text-xs text-muted-foreground">+2 from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalApplications}</div>
            <p className="text-xs text-muted-foreground">+23 this week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Interviews Scheduled</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.interviewsScheduled}</div>
            <p className="text-xs text-muted-foreground">This week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hired This Month</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.hiredCandidates}</div>
            <p className="text-xs text-muted-foreground">+1 from last month</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Jobs */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Job Postings</CardTitle>
            <CardDescription>Your latest job postings and their performance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentJobs.map((job) => (
                <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium">{job.title}</h4>
                    <p className="text-sm text-gray-600">{job.department}</p>
                    <div className="flex items-center gap-4 mt-1">
                      <span className="text-xs text-gray-500">{job.applications} applications</span>
                      <span className="text-xs text-gray-500">{job.views} views</span>
                    </div>
                  </div>
                  <Badge className={getStatusColor(job.status)}>
                    {getStatusText(job.status)}
                  </Badge>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4">
              View All Jobs
            </Button>
          </CardContent>
        </Card>

        {/* Recent Applications */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Applications</CardTitle>
            <CardDescription>Latest candidate applications</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentApplications.map((application) => (
                <div key={application.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium">{application.candidateName}</h4>
                    <p className="text-sm text-gray-600">{application.jobTitle}</p>
                    <p className="text-xs text-gray-500">
                      {application.experience} • Applied {application.appliedDate}
                    </p>
                  </div>
                  <Badge className={getStatusColor(application.status)}>
                    {getStatusText(application.status)}
                  </Badge>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4">
              View All Applications
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Upcoming Interviews */}
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Interviews</CardTitle>
          <CardDescription>Your scheduled interviews with candidates</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {upcomingInterviews.map((interview) => (
              <div key={interview.id} className="p-4 border rounded-lg">
                <h4 className="font-medium">{interview.candidateName}</h4>
                <p className="text-sm text-gray-600">{interview.jobTitle}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Calendar className="h-3 w-3 text-gray-500" />
                  <p className="text-xs text-gray-500">
                    {interview.date} at {interview.time}
                  </p>
                </div>
                <Badge variant="outline" className="mt-2">
                  {interview.type}
                </Badge>
              </div>
            ))}
          </div>
          <Button variant="outline" className="w-full mt-4">
            View All Interviews
          </Button>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common recruiting tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button className="h-20 flex flex-col items-center justify-center">
              <Plus className="h-6 w-6 mb-2" />
              Post Job
            </Button>
            <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
              <Users className="h-6 w-6 mb-2" />
              Browse Candidates
            </Button>
            <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
              <Calendar className="h-6 w-6 mb-2" />
              Schedule Interview
            </Button>
            <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
              <FileText className="h-6 w-6 mb-2" />
              View Reports
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
