'use client';

import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, MoreHorizontal, Eye, Edit, Trash2, MapPin, DollarSign } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DataTable, StatusBadge } from '@/components/ui/data-table';

export interface Job {
  id: string;
  jobId: string;
  title: string;
  description?: string;
  department?: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship';
  level: 'entry' | 'mid' | 'senior' | 'lead' | 'executive';
  salaryMin?: number;
  salaryMax?: number;
  currency?: string;
  status: 'draft' | 'published' | 'closed' | 'paused';
  organizationName: string;
  applicationCount?: number;
  viewCount?: number;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
}

interface JobsDataTableProps {
  data: Job[];
  isLoading?: boolean;
  onView?: (job: Job) => void;
  onEdit?: (job: Job) => void;
  onDelete?: (job: Job) => void;
  onStatusChange?: (job: Job, newStatus: string) => void;
}

export function JobsDataTable({
  data,
  isLoading = false,
  onView,
  onEdit,
  onDelete,
  onStatusChange,
}: JobsDataTableProps) {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'full-time':
        return 'bg-green-100 text-green-800';
      case 'part-time':
        return 'bg-blue-100 text-blue-800';
      case 'contract':
        return 'bg-yellow-100 text-yellow-800';
      case 'internship':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'entry':
        return 'bg-green-100 text-green-800';
      case 'mid':
        return 'bg-blue-100 text-blue-800';
      case 'senior':
        return 'bg-orange-100 text-orange-800';
      case 'lead':
        return 'bg-red-100 text-red-800';
      case 'executive':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatSalary = (min?: number, max?: number, currency = 'USD') => {
    if (!min && !max) return '-';
    if (min && max) {
      return `${currency} ${min.toLocaleString()} - ${max.toLocaleString()}`;
    }
    if (min) return `${currency} ${min.toLocaleString()}+`;
    if (max) return `Up to ${currency} ${max.toLocaleString()}`;
    return '-';
  };

  const columns: ColumnDef<Job>[] = [
    {
      accessorKey: 'title',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Job Title
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const job = row.original;
        return (
          <div>
            <div className="font-medium">{job.title}</div>
            <div className="text-sm text-muted-foreground">{job.organizationName}</div>
            {job.department && (
              <div className="text-xs text-muted-foreground">{job.department}</div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'location',
      header: 'Location',
      cell: ({ row }) => {
        const location = row.getValue('location') as string;
        return (
          <div className="flex items-center">
            <MapPin className="mr-1 h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{location}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }) => {
        const type = row.getValue('type') as string;
        return (
          <Badge className={getTypeColor(type)}>
            {type.replace('-', ' ')}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'level',
      header: 'Level',
      cell: ({ row }) => {
        const level = row.getValue('level') as string;
        return (
          <Badge className={getLevelColor(level)}>
            {level}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'salaryMin',
      header: 'Salary',
      cell: ({ row }) => {
        const job = row.original;
        return (
          <div className="flex items-center">
            <DollarSign className="mr-1 h-3 w-3 text-muted-foreground" />
            <span className="text-sm">
              {formatSalary(job.salaryMin, job.salaryMax, job.currency)}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return <StatusBadge status={status} />;
      },
    },
    {
      accessorKey: 'applicationCount',
      header: 'Applications',
      cell: ({ row }) => {
        const applicationCount = row.getValue('applicationCount') as number;
        return applicationCount || 0;
      },
    },
    {
      accessorKey: 'viewCount',
      header: 'Views',
      cell: ({ row }) => {
        const viewCount = row.getValue('viewCount') as number;
        return viewCount || 0;
      },
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Created
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const createdAt = row.getValue('createdAt') as string;
        return new Date(createdAt).toLocaleDateString();
      },
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const job = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(job.jobId)}
              >
                Copy job ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {onView && (
                <DropdownMenuItem onClick={() => onView(job)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View details
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(job)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit job
                </DropdownMenuItem>
              )}
              {onStatusChange && job.status !== 'closed' && (
                <DropdownMenuItem
                  onClick={() => onStatusChange(job, job.status === 'published' ? 'paused' : 'published')}
                >
                  {job.status === 'published' ? 'Pause' : 'Publish'}
                </DropdownMenuItem>
              )}
              {onStatusChange && job.status !== 'closed' && (
                <DropdownMenuItem
                  onClick={() => onStatusChange(job, 'closed')}
                >
                  Close job
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem
                  onClick={() => onDelete(job)}
                  className="text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete job
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="title"
      searchPlaceholder="Search jobs..."
      isLoading={isLoading}
      emptyMessage="No jobs found."
    />
  );
}
