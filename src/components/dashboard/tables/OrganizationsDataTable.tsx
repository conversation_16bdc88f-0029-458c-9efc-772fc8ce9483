'use client';

import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, MoreHorizontal, Eye, Edit, Trash2, Building2 } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DataTable, StatusBadge } from '@/components/ui/data-table';

export interface Organization {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  website?: string;
  industry?: string;
  size?: string;
  logo?: string;
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  ownerId: string;
  ownerName?: string;
  memberCount?: number;
  jobCount?: number;
  createdAt: string;
  updatedAt: string;
}

interface OrganizationsDataTableProps {
  data: Organization[];
  isLoading?: boolean;
  onView?: (organization: Organization) => void;
  onEdit?: (organization: Organization) => void;
  onDelete?: (organization: Organization) => void;
  onStatusChange?: (organization: Organization, newStatus: string) => void;
}

export function OrganizationsDataTable({
  data,
  isLoading = false,
  onView,
  onEdit,
  onDelete,
  onStatusChange,
}: OrganizationsDataTableProps) {
  const getSizeColor = (size: string) => {
    switch (size) {
      case '1-10':
        return 'bg-green-100 text-green-800';
      case '11-50':
        return 'bg-blue-100 text-blue-800';
      case '51-200':
        return 'bg-yellow-100 text-yellow-800';
      case '201-500':
        return 'bg-orange-100 text-orange-800';
      case '501-1000':
        return 'bg-red-100 text-red-800';
      case '1000+':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns: ColumnDef<Organization>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Organization
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const org = row.original;
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={org.logo} alt={org.name} />
              <AvatarFallback>
                <Building2 className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{org.name}</div>
              {org.website && (
                <div className="text-sm text-muted-foreground">{org.website}</div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'industry',
      header: 'Industry',
      cell: ({ row }) => {
        const industry = row.getValue('industry') as string;
        return industry ? (
          <Badge variant="outline">{industry}</Badge>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      accessorKey: 'size',
      header: 'Size',
      cell: ({ row }) => {
        const size = row.getValue('size') as string;
        return size ? (
          <Badge className={getSizeColor(size)}>
            {size} employees
          </Badge>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      accessorKey: 'status',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return <StatusBadge status={status} />;
      },
    },
    {
      accessorKey: 'ownerName',
      header: 'Owner',
      cell: ({ row }) => {
        const ownerName = row.getValue('ownerName') as string;
        return ownerName || <span className="text-muted-foreground">-</span>;
      },
    },
    {
      accessorKey: 'memberCount',
      header: 'Members',
      cell: ({ row }) => {
        const memberCount = row.getValue('memberCount') as number;
        return memberCount || 0;
      },
    },
    {
      accessorKey: 'jobCount',
      header: 'Jobs',
      cell: ({ row }) => {
        const jobCount = row.getValue('jobCount') as number;
        return jobCount || 0;
      },
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Created
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const createdAt = row.getValue('createdAt') as string;
        return new Date(createdAt).toLocaleDateString();
      },
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const organization = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(organization.organizationId)}
              >
                Copy organization ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {onView && (
                <DropdownMenuItem onClick={() => onView(organization)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View details
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(organization)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit organization
                </DropdownMenuItem>
              )}
              {onStatusChange && (
                <DropdownMenuItem
                  onClick={() => onStatusChange(organization, organization.status === 'active' ? 'inactive' : 'active')}
                >
                  {organization.status === 'active' ? 'Deactivate' : 'Activate'}
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem
                  onClick={() => onDelete(organization)}
                  className="text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete organization
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="name"
      searchPlaceholder="Search organizations..."
      isLoading={isLoading}
      emptyMessage="No organizations found."
    />
  );
}
