'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Icons } from '@/components/ui/icons';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { api } from '@/trpc/react';

const MagicLinkSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type MagicLinkFormData = z.infer<typeof MagicLinkSchema>;

interface MagicLinkFormProps {
  purpose?: 'login' | 'verification' | 'password-reset';
  redirectTo?: string;
  onSuccess?: () => void;
  className?: string;
}

export function MagicLinkForm({
  purpose = 'login',
  redirectTo,
  onSuccess,
  className,
}: MagicLinkFormProps) {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    getValues,
  } = useForm<MagicLinkFormData>({
    resolver: zodResolver(MagicLinkSchema),
  });

  const sendMagicLinkMutation = api.auth.sendMagicLink.useMutation({
    onSuccess: () => {
      setIsSubmitted(true);
      setError(null);
      onSuccess?.();
    },
    onError: (error) => {
      setError(error.message);
    },
  });

  const onSubmit = async (data: MagicLinkFormData) => {
    try {
      await sendMagicLinkMutation.mutateAsync({
        email: data.email,
        purpose,
        redirectTo,
      });
    } catch {
      // Error is handled by onError callback
    }
  };

  const handleResend = () => {
    const email = getValues('email');
    if (email) {
      sendMagicLinkMutation.mutate({
        email,
        purpose,
        redirectTo,
      });
    }
  };

  if (isSubmitted) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
            <Icons.mail className="h-6 w-6 text-green-600" />
          </div>
          <h3 className="mt-2 text-lg font-medium">Check your email</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            We&apos;ve sent a magic link to <strong>{getValues('email')}</strong>
          </p>
        </div>

        <Alert>
          <Icons.info className="h-4 w-4" />
          <AlertDescription>
            Click the link in your email to{' '}
            {purpose === 'login'
              ? 'sign in'
              : purpose === 'verification'
                ? 'verify your account'
                : 'reset your password'}
            . The link will expire in 15 minutes.
          </AlertDescription>
        </Alert>

        <div className="flex flex-col space-y-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleResend}
            disabled={sendMagicLinkMutation.isPending}
          >
            {sendMagicLinkMutation.isPending && (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            )}
            Resend magic link
          </Button>

          <Button type="button" variant="ghost" onClick={() => setIsSubmitted(false)}>
            Use a different email
          </Button>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className={`space-y-4 ${className}`}>
      <div className="space-y-2">
        <Label htmlFor="email">Email address</Label>
        <Input
          id="email"
          type="email"
          placeholder="Enter your email"
          {...register('email')}
          disabled={isSubmitting}
        />
        {errors.email && <p className="text-sm text-destructive">{errors.email.message}</p>}
      </div>

      {error && (
        <Alert variant="destructive">
          <Icons.alertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Button type="submit" className="w-full" disabled={isSubmitting}>
        {isSubmitting && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
        Send magic link
      </Button>

      <div className="text-center">
        <p className="text-sm text-muted-foreground">
          We&apos;ll send you a secure link to{' '}
          {purpose === 'login'
            ? 'sign in'
            : purpose === 'verification'
              ? 'verify your account'
              : 'reset your password'}{' '}
          without a password.
        </p>
      </div>
    </form>
  );
}

export default MagicLinkForm;
