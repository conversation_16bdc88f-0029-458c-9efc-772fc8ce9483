'use client';

import React, { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Icons } from '@/components/ui/icons';
import { api } from '@/trpc/react';

interface OAuthButtonsProps {
  redirectTo?: string;
  disabled?: boolean;
  className?: string;
}

export function OAuthButtons({ redirectTo, disabled = false, className }: OAuthButtonsProps) {
  const [loadingProvider, setLoadingProvider] = useState<string | null>(null);

  const getOAuthUrlMutation = api.auth.getOAuthUrl.useMutation({
    onSuccess: (data) => {
      if (data.authUrl) {
        window.location.href = data.authUrl?.toString();
      }
    },
    onError: (error) => {
      console.error('OAuth URL generation failed:', error);
      setLoadingProvider(null);
    },
  });

  const handleOAuthLogin = async (provider: 'google' | 'microsoft') => {
    setLoadingProvider(provider);
    try {
      await getOAuthUrlMutation.mutateAsync({
        provider,
        redirectTo,
      });
    } catch {
      setLoadingProvider(null);
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <Button
        type="button"
        variant="outline"
        className="w-full"
        disabled={disabled || loadingProvider === 'google'}
        onClick={() => handleOAuthLogin('google')}
      >
        {loadingProvider === 'google' ? (
          <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <Icons.google className="mr-2 h-4 w-4" />
        )}
        Continue with Google
      </Button>

      <Button
        type="button"
        variant="outline"
        className="w-full"
        disabled={disabled || loadingProvider === 'microsoft'}
        onClick={() => handleOAuthLogin('microsoft')}
      >
        {loadingProvider === 'microsoft' ? (
          <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <Icons.microsoft className="mr-2 h-4 w-4" />
        )}
        Continue with Microsoft
      </Button>
    </div>
  );
}

export default OAuthButtons;
