'use client';

import { usePathname, useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { useAuth } from '@/hooks/useAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string | string[];
  fallback?: React.ComponentType;
  loadingComponent?: React.ComponentType;
  unauthorizedComponent?: React.ComponentType;
  redirectTo?: string;
  redirectToUnauthorized?: string;
}

export default function ProtectedRoute({
  children,
  requiredRole,
  fallback: FallbackComponent,
  loadingComponent: LoadingComponent,
  unauthorizedComponent: UnauthorizedComponent,
  redirectTo = '/login',
  redirectToUnauthorized = '/unauthorized',
}: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    if (isLoading) {
      setShouldRender(false);
      return;
    }

    if (!isAuthenticated) {
      // Not authenticated, redirect to login
      const loginUrl = new URL(redirectTo, window.location.origin);
      loginUrl.searchParams.set('redirect', pathname);
      router.push(loginUrl.toString());
      setShouldRender(false);
      return;
    }

    if (requiredRole && user) {
      // Check role requirements
      const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
      if (!roles.includes(user.role)) {
        // User doesn't have required role
        router.push(redirectToUnauthorized);
        setShouldRender(false);
        return;
      }
    }

    // All checks passed
    setShouldRender(true);
  }, [
    isLoading,
    isAuthenticated,
    user,
    requiredRole,
    router,
    pathname,
    redirectTo,
    redirectToUnauthorized,
  ]);

  // Show loading state
  if (isLoading) {
    if (LoadingComponent) {
      return <LoadingComponent />;
    }
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // Show unauthorized state
  if (!isAuthenticated && FallbackComponent) {
    return <FallbackComponent />;
  }

  // Show role unauthorized state
  if (isAuthenticated && requiredRole && user) {
    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    if (!roles.includes(user.role)) {
      if (UnauthorizedComponent) {
        return <UnauthorizedComponent />;
      }
      if (FallbackComponent) {
        return <FallbackComponent />;
      }
    }
  }

  // Render children if all checks pass
  return shouldRender ? <>{children}</> : null;
}

// Higher-order component version
export function withProtectedRoute<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ProtectedRouteProps, 'children'> = {}
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// Specific role-based components
export function AdminRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRole'>) {
  return (
    <ProtectedRoute requiredRole="admin" {...props}>
      {children}
    </ProtectedRoute>
  );
}

export function RecruiterRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRole'>) {
  return (
    <ProtectedRoute requiredRole={['admin', 'recruiter', 'hr']} {...props}>
      {children}
    </ProtectedRoute>
  );
}

export function HRRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRole'>) {
  return (
    <ProtectedRoute requiredRole={['admin', 'hr']} {...props}>
      {children}
    </ProtectedRoute>
  );
}

// Auth guard hook for conditional rendering
export function useAuthGuard(requiredRole?: string | string[]) {
  const { user, isLoading, isAuthenticated } = useAuth();

  const canAccess = React.useMemo(() => {
    if (isLoading) return false;
    if (!isAuthenticated) return false;

    if (requiredRole && user) {
      const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
      return roles.includes(user.role);
    }

    return true;
  }, [isLoading, isAuthenticated, user, requiredRole]);

  return {
    canAccess,
    isLoading,
    isAuthenticated,
    user,
  };
}

// Component for conditional rendering based on auth
export function AuthGuard({
  children,
  requiredRole,
  fallback,
  showLoading = true,
}: {
  children: React.ReactNode;
  requiredRole?: string | string[];
  fallback?: React.ReactNode;
  showLoading?: boolean;
}) {
  const { canAccess, isLoading } = useAuthGuard(requiredRole);

  if (isLoading && showLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!canAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Hook for protecting actions
export function useProtectedAction() {
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();

  const executeIfAuthenticated = React.useCallback(
    <T extends any[], R>(action: (...args: T) => R, redirectTo = '/login') => {
      return (...args: T): R | void => {
        if (!isAuthenticated) {
          router.push(redirectTo);
          return;
        }
        return action(...args);
      };
    },
    [isAuthenticated, router]
  );

  const executeIfRole = React.useCallback(
    <T extends any[], R>(
      action: (...args: T) => R,
      requiredRole: string | string[],
      fallback?: () => void
    ) => {
      return (...args: T): R | void => {
        if (!isAuthenticated || !user) {
          router.push('/login');
          return;
        }

        const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
        if (!roles.includes(user.role)) {
          if (fallback) {
            fallback();
          } else {
            router.push('/unauthorized');
          }
          return;
        }

        return action(...args);
      };
    },
    [isAuthenticated, user, router]
  );

  return {
    executeIfAuthenticated,
    executeIfRole,
  };
}
