import * as arctic from 'arctic';

import { env } from '@/env';

// OAuth providers configuration
export const google = new arctic.Google(
  env.GOOGLE_OAUTH_CLIENT_ID,
  env.GOOGLE_OAUTH_CLIENT_SECRET,
  `${env.APP_URL}/auth/callback/google`
);

export const microsoft = new arctic.MicrosoftEntraId(
  '121',
  env.MICROSOFT_OAUTH_CLIENT_ID,
  env.MICROSOFT_OAUTH_CLIENT_SECRET,
  `${env.APP_URL}/auth/callback/microsoft`
);

// OAuth scopes
export const GOOGLE_SCOPES = ['openid', 'profile', 'email'];

export const MICROSOFT_SCOPES = ['openid', 'profile', 'email', 'User.Read'];

// OAuth state management
export interface OAuthState {
  provider: 'google' | 'microsoft';
  redirectTo?: string;
  timestamp: number;
}

export function generateOAuthState(provider: 'google' | 'microsoft', redirectTo?: string): string {
  const state: OAuthState = {
    provider,
    redirectTo,
    timestamp: Date.now(),
  };

  return Buffer.from(JSON.stringify(state)).toString('base64url');
}

export function parseOAuthState(stateString: string): OAuthState | null {
  try {
    const decoded = Buffer.from(stateString, 'base64url').toString('utf-8');
    const state = JSON.parse(decoded) as OAuthState;

    // Check if state is expired (15 minutes)
    if (Date.now() - state.timestamp > 15 * 60 * 1000) {
      return null;
    }

    return state;
  } catch {
    return null;
  }
}

// OAuth user profile interfaces
export interface GoogleUserProfile {
  sub: string;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  email: string;
  email_verified: boolean;
  locale?: string;
}

export interface MicrosoftUserProfile {
  id: string;
  displayName: string;
  givenName: string;
  surname: string;
  userPrincipalName: string;
  mail: string;
  mobilePhone?: string;
  jobTitle?: string;
  officeLocation?: string;
}

// Fetch user profile from OAuth providers
export async function fetchGoogleUserProfile(accessToken: string): Promise<GoogleUserProfile> {
  const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch Google user profile');
  }

  return response.json();
}

export async function fetchMicrosoftUserProfile(
  accessToken: string
): Promise<MicrosoftUserProfile> {
  const response = await fetch('https://graph.microsoft.com/v1.0/me', {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch Microsoft user profile');
  }

  return response.json();
}

// Normalize user data from different providers
export interface NormalizedOAuthUser {
  providerId: string;
  provider: 'google' | 'microsoft';
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  isEmailVerified: boolean;
}

export function normalizeGoogleUser(profile: GoogleUserProfile): NormalizedOAuthUser {
  return {
    providerId: profile.sub,
    provider: 'google',
    email: profile.email,
    firstName: profile.given_name,
    lastName: profile.family_name,
    avatar: profile.picture,
    isEmailVerified: profile.email_verified,
  };
}

export function normalizeMicrosoftUser(profile: MicrosoftUserProfile): NormalizedOAuthUser {
  return {
    providerId: profile.id,
    provider: 'microsoft',
    email: profile.mail || profile.userPrincipalName,
    firstName: profile.givenName,
    lastName: profile.surname,
    avatar: undefined, // Microsoft Graph doesn't provide avatar URL directly
    isEmailVerified: true, // Microsoft accounts are typically verified
  };
}

// OAuth error handling
export class OAuthError extends Error {
  constructor(
    public provider: string,
    public code: string,
    message: string
  ) {
    super(message);
    this.name = 'OAuthError';
  }
}

export function handleOAuthError(error: unknown, provider: string): never {
  if (error instanceof Error) {
    throw new OAuthError(provider, 'OAUTH_ERROR', error.message);
  }

  throw new OAuthError(provider, 'UNKNOWN_ERROR', 'An unknown OAuth error occurred');
}

// OAuth session management
export interface OAuthSession {
  provider: 'google' | 'microsoft';
  accessToken: string;
  refreshToken?: string;
  expiresAt: number;
  userId: string;
}

export function createOAuthSession(
  provider: 'google' | 'microsoft',
  accessToken: string,
  userId: string,
  expiresIn?: number,
  refreshToken?: string
): OAuthSession {
  const expiresAt = expiresIn ? Date.now() + expiresIn * 1000 : Date.now() + 3600 * 1000; // Default 1 hour

  return {
    provider,
    accessToken,
    refreshToken,
    expiresAt,
    userId,
  };
}

export function isOAuthSessionExpired(session: OAuthSession): boolean {
  return Date.now() >= session.expiresAt;
}

// OAuth provider URLs
export function getOAuthAuthorizationUrl(provider: 'google' | 'microsoft', state: string): URL {
  const codeVerifier = arctic.generateCodeVerifier();
  const scopes = ['openid', 'profile'];

  switch (provider) {
    case 'google':
      return google.createAuthorizationURL(state, codeVerifier, scopes);
    case 'microsoft':
      return microsoft.createAuthorizationURL(state, codeVerifier, scopes);
    default:
      throw new Error(`Unsupported OAuth provider: ${provider}`);
  }
}

// OAuth token exchange
export async function exchangeOAuthCode(
  provider: 'google' | 'microsoft',
  code: string
): Promise<{ accessToken: string; refreshToken?: string; expiresIn?: Date }> {
  try {
    const codeVerifier = arctic.generateCodeVerifier();
    switch (provider) {
      case 'google': {
        const tokens = await google.validateAuthorizationCode(code, codeVerifier);
        return {
          accessToken: tokens.accessToken(),
          refreshToken: tokens.refreshToken(),
          expiresIn: tokens.accessTokenExpiresAt(),
        };
      }
      case 'microsoft': {
        const tokens = await microsoft.validateAuthorizationCode(code, codeVerifier);
        return {
          accessToken: tokens.accessToken(),
          refreshToken: tokens.refreshToken(),
          expiresIn: tokens.accessTokenExpiresAt(),
        };
      }
      default:
        throw new Error(`Unsupported OAuth provider: ${provider}`);
    }
  } catch (error) {
    handleOAuthError(error, provider);
  }
}
