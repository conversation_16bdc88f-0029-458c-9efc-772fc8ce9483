import crypto from 'crypto';

import { env } from '@/env';

// Magic link configuration
const MAGIC_LINK_SECRET = env.MAGIC_LINK_SECRET;
const MAGIC_LINK_EXPIRES_IN = env.MAGIC_LINK_EXPIRES_IN * 1000; // Convert to milliseconds

// Magic link payload interface
export interface MagicLinkPayload {
  email: string;
  purpose: 'login' | 'verification' | 'password-reset';
  timestamp: number;
  redirectTo?: string;
}

// Generate magic link token
export function generateMagicLinkToken(payload: MagicLinkPayload): string {
  const data = JSON.stringify(payload);

  // Create HMAC signature
  const hmac = crypto.createHmac('sha256', MAGIC_LINK_SECRET);
  hmac.update(data);
  const signature = hmac.digest('base64url');

  // Combine data and signature
  const token = {
    data: Buffer.from(data).toString('base64url'),
    signature,
  };

  return Buffer.from(JSON.stringify(token)).toString('base64url');
}

// Verify and decode magic link token
export function verifyMagicLinkToken(token: string): MagicLinkPayload | null {
  try {
    // Decode token
    const tokenData = JSON.parse(Buffer.from(token, 'base64url').toString());

    // Extract data and signature
    const data = Buffer.from(tokenData.data, 'base64url').toString();
    const signature = tokenData.signature;

    // Verify signature
    const hmac = crypto.createHmac('sha256', MAGIC_LINK_SECRET);
    hmac.update(data);
    const expectedSignature = hmac.digest('base64url');

    // Compare signatures (constant time comparison)
    if (
      !crypto.timingSafeEqual(
        Buffer.from(signature, 'base64url'),
        Buffer.from(expectedSignature, 'base64url')
      )
    ) {
      return null;
    }

    // Decode payload
    const payload = JSON.parse(data) as MagicLinkPayload;

    // Check expiration
    if (Date.now() - payload.timestamp > MAGIC_LINK_EXPIRES_IN) {
      return null;
    }

    return payload;
  } catch {
    return null;
  }
}

// Generate magic link URL
export function generateMagicLink(
  email: string,
  purpose: 'login' | 'verification' | 'password-reset' = 'login',
  redirectTo?: string
): string {
  const payload: MagicLinkPayload = {
    email,
    purpose,
    timestamp: Date.now(),
    redirectTo,
  };

  const token = generateMagicLinkToken(payload);
  return `${env.APP_URL}/auth/magic-link/verify?token=${token}`;
}

// Magic link storage interface (for tracking used links)
export interface MagicLinkRecord {
  id: string;
  email: string;
  purpose: string;
  token: string;
  used: boolean;
  createdAt: Date;
  expiresAt: Date;
  usedAt?: Date;
}

// In-memory storage for demo (replace with database in production)
const usedTokens = new Set<string>();

// Mark token as used
export function markTokenAsUsed(token: string): void {
  usedTokens.add(token);
}

// Check if token has been used
export function isTokenUsed(token: string): boolean {
  return usedTokens.has(token);
}

// Clean up expired tokens (call this periodically)
export function cleanupExpiredTokens(): void {
  // In a real implementation, this would clean up database records
  // For now, we'll just clear the in-memory set periodically
  if (usedTokens.size > 1000) {
    usedTokens.clear();
  }
}

// Magic link validation result
export interface MagicLinkValidationResult {
  valid: boolean;
  payload?: MagicLinkPayload;
  error?: string;
}

// Validate magic link token with comprehensive checks
export function validateMagicLinkToken(token: string): MagicLinkValidationResult {
  // Check if token has been used
  if (isTokenUsed(token)) {
    return {
      valid: false,
      error: 'Magic link has already been used',
    };
  }

  // Verify token
  const payload = verifyMagicLinkToken(token);

  if (!payload) {
    return {
      valid: false,
      error: 'Invalid or expired magic link',
    };
  }

  return {
    valid: true,
    payload,
  };
}

// Generate secure magic link with rate limiting consideration
export async function createSecureMagicLink(
  email: string,
  purpose: 'login' | 'verification' | 'password-reset' = 'login',
  redirectTo?: string
): Promise<{
  magicLink: string;
  token: string;
  expiresAt: Date;
}> {
  const token = generateMagicLinkToken({
    email,
    purpose,
    timestamp: Date.now(),
    redirectTo,
  });

  const magicLink = `${env.APP_URL}/api/auth/magic-link/verify?token=${token}`;
  const expiresAt = new Date(Date.now() + MAGIC_LINK_EXPIRES_IN);

  return {
    magicLink,
    token,
    expiresAt,
  };
}

// Magic link rate limiting (simple implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkMagicLinkRateLimit(email: string): {
  allowed: boolean;
  remainingAttempts: number;
  resetTime: number;
} {
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutes
  const maxAttempts = 3;

  const key = email.toLowerCase();
  const current = rateLimitMap.get(key);

  if (!current || now > current.resetTime) {
    // Reset or initialize
    rateLimitMap.set(key, {
      count: 1,
      resetTime: now + windowMs,
    });

    return {
      allowed: true,
      remainingAttempts: maxAttempts - 1,
      resetTime: now + windowMs,
    };
  }

  if (current.count >= maxAttempts) {
    return {
      allowed: false,
      remainingAttempts: 0,
      resetTime: current.resetTime,
    };
  }

  current.count++;

  return {
    allowed: true,
    remainingAttempts: maxAttempts - current.count,
    resetTime: current.resetTime,
  };
}

// Clean up rate limit map periodically
export function cleanupRateLimit(): void {
  const now = Date.now();

  for (const [key, value] of rateLimitMap.entries()) {
    if (now > value.resetTime) {
      rateLimitMap.delete(key);
    }
  }
}

// Magic link error types
export class MagicLinkError extends Error {
  constructor(
    public code: string,
    message: string
  ) {
    super(message);
    this.name = 'MagicLinkError';
  }
}

export const MAGIC_LINK_ERRORS = {
  INVALID_TOKEN: 'INVALID_TOKEN',
  EXPIRED_TOKEN: 'EXPIRED_TOKEN',
  USED_TOKEN: 'USED_TOKEN',
  RATE_LIMITED: 'RATE_LIMITED',
  EMAIL_SEND_FAILED: 'EMAIL_SEND_FAILED',
} as const;
