// OTP configuration
const OTP_LENGTH = 6;
const OTP_EXPIRES_IN = 10 * 60 * 1000; // 10 minutes in milliseconds
const OTP_ALPHABET = '0123456789'; // Numeric OTP

const generateRandomOTP = (length: number, alphabet: string): string => {
  let otp = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * alphabet.length);
    otp += alphabet[randomIndex];
  }
  return otp;
};

// OTP record interface
export interface OTPRecord {
  id: string;
  email: string;
  code: string;
  purpose: 'login' | 'verification' | 'password-reset';
  attempts: number;
  maxAttempts: number;
  createdAt: Date;
  expiresAt: Date;
  verified: boolean;
  verifiedAt?: Date;
}

// In-memory OTP storage (replace with database in production)
const otpStorage = new Map<string, OTPRecord>();

// Generate OTP code
export function generateOTP(): string {
  return generateRandomOTP(OTP_LENGTH, OTP_ALPHABET);
}

// Create OTP record
export function createOTPRecord(
  email: string,
  purpose: 'login' | 'verification' | 'password-reset' = 'login'
): OTPRecord {
  const code = generateOTP();
  const now = new Date();
  const expiresAt = new Date(now.getTime() + OTP_EXPIRES_IN);

  const record: OTPRecord = {
    id: `${email}-${purpose}-${Date.now()}`,
    email: email.toLowerCase(),
    code,
    purpose,
    attempts: 0,
    maxAttempts: 3,
    createdAt: now,
    expiresAt,
    verified: false,
  };

  // Store OTP (in production, save to database)
  const key = `${email.toLowerCase()}-${purpose}`;
  otpStorage.set(key, record);

  return record;
}

// Verify OTP code
export interface OTPVerificationResult {
  valid: boolean;
  record?: OTPRecord;
  error?: string;
  attemptsRemaining?: number;
}

export function verifyOTP(
  email: string,
  code: string,
  purpose: 'login' | 'verification' | 'password-reset' = 'login'
): OTPVerificationResult {
  const key = `${email.toLowerCase()}-${purpose}`;
  const record = otpStorage.get(key);

  if (!record) {
    return {
      valid: false,
      error: 'No OTP found for this email and purpose',
    };
  }

  // Check if already verified
  if (record.verified) {
    return {
      valid: false,
      error: 'OTP has already been used',
    };
  }

  // Check expiration
  if (new Date() > record.expiresAt) {
    otpStorage.delete(key);
    return {
      valid: false,
      error: 'OTP has expired',
    };
  }

  // Check max attempts
  if (record.attempts >= record.maxAttempts) {
    otpStorage.delete(key);
    return {
      valid: false,
      error: 'Maximum verification attempts exceeded',
    };
  }

  // Increment attempts
  record.attempts++;

  // Verify code
  if (record.code !== code) {
    const attemptsRemaining = record.maxAttempts - record.attempts;

    if (attemptsRemaining <= 0) {
      otpStorage.delete(key);
      return {
        valid: false,
        error: 'Invalid OTP. Maximum attempts exceeded.',
      };
    }

    return {
      valid: false,
      error: 'Invalid OTP code',
      attemptsRemaining,
    };
  }

  // Mark as verified
  record.verified = true;
  record.verifiedAt = new Date();

  // Remove from storage after successful verification
  otpStorage.delete(key);

  return {
    valid: true,
    record,
  };
}

// Get OTP record
export function getOTPRecord(
  email: string,
  purpose: 'login' | 'verification' | 'password-reset' = 'login'
): OTPRecord | null {
  const key = `${email.toLowerCase()}-${purpose}`;
  return otpStorage.get(key) || null;
}

// Delete OTP record
export function deleteOTPRecord(
  email: string,
  purpose: 'login' | 'verification' | 'password-reset' = 'login'
): boolean {
  const key = `${email.toLowerCase()}-${purpose}`;
  return otpStorage.delete(key);
}

// Clean up expired OTPs
export function cleanupExpiredOTPs(): void {
  const now = new Date();

  for (const [key, record] of otpStorage.entries()) {
    if (now > record.expiresAt) {
      otpStorage.delete(key);
    }
  }
}

// OTP rate limiting
const otpRateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkOTPRateLimit(email: string): {
  allowed: boolean;
  remainingAttempts: number;
  resetTime: number;
} {
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutes
  const maxAttempts = 5; // Allow 5 OTP requests per 15 minutes

  const key = email.toLowerCase();
  const current = otpRateLimitMap.get(key);

  if (!current || now > current.resetTime) {
    // Reset or initialize
    otpRateLimitMap.set(key, {
      count: 1,
      resetTime: now + windowMs,
    });

    return {
      allowed: true,
      remainingAttempts: maxAttempts - 1,
      resetTime: now + windowMs,
    };
  }

  if (current.count >= maxAttempts) {
    return {
      allowed: false,
      remainingAttempts: 0,
      resetTime: current.resetTime,
    };
  }

  current.count++;

  return {
    allowed: true,
    remainingAttempts: maxAttempts - current.count,
    resetTime: current.resetTime,
  };
}

// Clean up rate limit map
export function cleanupOTPRateLimit(): void {
  const now = Date.now();

  for (const [key, value] of otpRateLimitMap.entries()) {
    if (now > value.resetTime) {
      otpRateLimitMap.delete(key);
    }
  }
}

// OTP validation with comprehensive checks
export interface OTPValidationOptions {
  email: string;
  code: string;
  purpose?: 'login' | 'verification' | 'password-reset';
  deleteAfterVerification?: boolean;
}

export function validateOTP(options: OTPValidationOptions): OTPVerificationResult {
  const { email, code, purpose = 'login', deleteAfterVerification = true } = options;

  const result = verifyOTP(email, code, purpose);

  if (result.valid && deleteAfterVerification) {
    deleteOTPRecord(email, purpose);
  }

  return result;
}

// Generate and store OTP
export async function generateAndStoreOTP(
  email: string,
  purpose: 'login' | 'verification' | 'password-reset' = 'login'
): Promise<{
  record: OTPRecord;
  code: string;
}> {
  // Clean up any existing OTP for this email/purpose
  deleteOTPRecord(email, purpose);

  // Create new OTP record
  const record = createOTPRecord(email, purpose);

  return {
    record,
    code: record.code,
  };
}

// OTP statistics
export function getOTPStats(): {
  totalActive: number;
  byPurpose: Record<string, number>;
  expiringSoon: number; // Expiring in next 2 minutes
} {
  const now = new Date();
  const soonThreshold = new Date(now.getTime() + 2 * 60 * 1000); // 2 minutes

  let totalActive = 0;
  let expiringSoon = 0;
  const byPurpose: Record<string, number> = {};

  for (const record of otpStorage.values()) {
    if (now <= record.expiresAt && !record.verified) {
      totalActive++;
      byPurpose[record.purpose] = (byPurpose[record.purpose] || 0) + 1;

      if (record.expiresAt <= soonThreshold) {
        expiringSoon++;
      }
    }
  }

  return {
    totalActive,
    byPurpose,
    expiringSoon,
  };
}

// OTP error types
export class OTPError extends Error {
  constructor(
    public code: string,
    message: string
  ) {
    super(message);
    this.name = 'OTPError';
  }
}

export const OTP_ERRORS = {
  INVALID_CODE: 'INVALID_CODE',
  EXPIRED_CODE: 'EXPIRED_CODE',
  MAX_ATTEMPTS_EXCEEDED: 'MAX_ATTEMPTS_EXCEEDED',
  ALREADY_VERIFIED: 'ALREADY_VERIFIED',
  NOT_FOUND: 'NOT_FOUND',
  RATE_LIMITED: 'RATE_LIMITED',
  EMAIL_SEND_FAILED: 'EMAIL_SEND_FAILED',
} as const;

// Utility function to format time remaining
export function formatTimeRemaining(expiresAt: Date): string {
  const now = new Date();
  const diff = expiresAt.getTime() - now.getTime();

  if (diff <= 0) {
    return 'Expired';
  }

  const minutes = Math.floor(diff / (60 * 1000));
  const seconds = Math.floor((diff % (60 * 1000)) / 1000);

  if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  }

  return `${seconds}s`;
}
