import jwt from 'jsonwebtoken';
import { cookies, headers } from 'next/headers';

import { User } from '@/db/models';
import dbConnect from '@/db/mongoose';
import { env } from '@/env';

// JWT configuration
const JWT_ACCESS_SECRET = env.JWT_ACCESS_SECRET;

interface DecodedToken {
  userId: string;
  email: string;
  role: string;
  type: string;
  iat: number;
  exp: number;
}

interface AuthUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  status: string;
  isEmailVerified: boolean;
}

/**
 * Verify JWT access token
 */
function verifyAccessToken(token: string): DecodedToken | null {
  try {
    const decoded = jwt.verify(token, JWT_ACCESS_SECRET) as DecodedToken;
    return decoded.type === 'access' ? decoded : null;
  } catch (_error) {
    return null;
  }
}

/**
 * Get current user from server-side (for server components and API routes)
 */
export async function getCurrentUser(): Promise<AuthUser | null> {
  try {
    await dbConnect();

    // Try to get token from cookies first
    const cookieStore = await cookies();
    let token = cookieStore.get('accessToken')?.value;

    // If no token in cookies, try headers (for API routes)
    if (!token) {
      const headersList = await headers();
      const authHeader = headersList.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.replace('Bearer ', '');
      }
    }

    // If still no token, try user info from middleware headers
    if (!token) {
      const headersList = await headers();
      const userId = headersList.get('x-user-id');
      const userEmail = headersList.get('x-user-email');
      const userRole = headersList.get('x-user-role');

      if (userId && userEmail && userRole) {
        // Get full user data from database
        const user = await User.findOne({
          userId,
          isDeleted: { $ne: true },
        });

        if (user) {
          return {
            id: user.userId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            status: user.status,
            isEmailVerified: user.isEmailVerified,
          };
        }
      }
    }

    if (!token) {
      return null;
    }

    // Verify token
    const decoded = verifyAccessToken(token);
    if (!decoded) {
      return null;
    }

    // Get user from database
    const user = await User.findOne({
      userId: decoded.userId,
      isDeleted: { $ne: true },
    });

    if (!user) {
      return null;
    }

    return {
      id: user.userId,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      status: user.status,
      isEmailVerified: user.isEmailVerified,
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Check if user is authenticated (for server components)
 */
export async function isAuthenticated(): Promise<boolean> {
  const user = await getCurrentUser();
  return !!user;
}

/**
 * Check if user has required role (for server components)
 */
export async function hasRole(requiredRole: string | string[]): Promise<boolean> {
  const user = await getCurrentUser();
  if (!user) return false;

  const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
  return roles.includes(user.role);
}

/**
 * Check if user is admin (for server components)
 */
export async function isAdmin(): Promise<boolean> {
  return hasRole('admin');
}

/**
 * Check if user is recruiter or higher (for server components)
 */
export async function isRecruiter(): Promise<boolean> {
  return hasRole(['admin', 'recruiter', 'hr']);
}

/**
 * Require authentication (throws error if not authenticated)
 */
export async function requireAuth(): Promise<AuthUser> {
  const user = await getCurrentUser();
  if (!user) {
    throw new Error('Authentication required');
  }
  return user;
}

/**
 * Require specific role (throws error if user doesn't have required role)
 */
export async function requireRole(requiredRole: string | string[]): Promise<AuthUser> {
  const user = await requireAuth();
  const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];

  if (!roles.includes(user.role)) {
    throw new Error(`Required role: ${roles.join(' or ')}`);
  }

  return user;
}

/**
 * Require admin role (throws error if user is not admin)
 */
export async function requireAdmin(): Promise<AuthUser> {
  return requireRole('admin');
}

/**
 * Require recruiter role or higher (throws error if user doesn't have permission)
 */
export async function requireRecruiter(): Promise<AuthUser> {
  return requireRole(['admin', 'recruiter', 'hr']);
}

/**
 * Get user session info for server actions
 */
export async function getSession(): Promise<{
  user: AuthUser | null;
  isAuthenticated: boolean;
}> {
  const user = await getCurrentUser();
  return {
    user,
    isAuthenticated: !!user,
  };
}

/**
 * Role hierarchy check
 */
export async function hasMinimumRole(minimumRole: string): Promise<boolean> {
  const user = await getCurrentUser();
  if (!user) return false;

  const roleHierarchy: Record<string, number> = {
    candidate: 1,
    interviewer: 2,
    hr: 3,
    recruiter: 4,
    admin: 5,
  };

  const userLevel = roleHierarchy[user.role] || 0;
  const requiredLevel = roleHierarchy[minimumRole] || 0;

  return userLevel >= requiredLevel;
}

/**
 * Check if user can access organization
 */
export async function canAccessOrganization(organizationId: string): Promise<boolean> {
  const user = await getCurrentUser();
  if (!user) return false;

  // Admins can access any organization
  if (user.role === 'admin') return true;

  // Check if user is a member of the organization
  try {
    const { OrganizationMember } = await import('@/db/models');
    const membership = await OrganizationMember.findOne({
      organizationId,
      userId: user.id,
      status: 'active',
      isDeleted: { $ne: true },
    });

    return !!membership;
  } catch (error) {
    console.error('Error checking organization access:', error);
    return false;
  }
}

/**
 * Get user's organization memberships
 */
export async function getUserOrganizations(): Promise<string[]> {
  const user = await getCurrentUser();
  if (!user) return [];

  try {
    const { OrganizationMember } = await import('@/db/models');
    const memberships = await OrganizationMember.find({
      userId: user.id,
      status: 'active',
      isDeleted: { $ne: true },
    }).select('organizationId');

    return memberships.map((m) => m.organizationId);
  } catch (error) {
    console.error('Error getting user organizations:', error);
    return [];
  }
}
