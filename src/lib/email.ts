import nodemailer from 'nodemailer';

import { env } from '@/env';

// Email transporter configuration
const transporter = nodemailer.createTransport({
  host: env.SMTP_HOST,
  port: env.SMTP_PORT,
  secure: env.SMTP_PORT === 465, // true for 465, false for other ports
  auth: {
    user: env.SMTP_USER,
    pass: env.SMTP_PASSWORD,
  },
});

// Verify email configuration
export async function verifyEmailConfig(): Promise<boolean> {
  try {
    await transporter.verify();
    return true;
  } catch (error) {
    console.error('Email configuration error:', error);
    return false;
  }
}

// Email templates
export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

// Magic link email template
export function createMagicLinkEmail(magicLink: string, userEmail: string): EmailTemplate {
  const subject = 'Sign in to Hirelytics';

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Sign in to Hirelytics</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
        .content { background: #f8fafc; padding: 30px; border-radius: 8px; margin: 20px 0; }
        .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
        .button:hover { background: #1d4ed8; }
        .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #6b7280; }
        .warning { background: #fef3cd; border: 1px solid #fbbf24; padding: 15px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">Hirelytics</div>
        </div>
        
        <div class="content">
          <h2>Sign in to your account</h2>
          <p>Hello,</p>
          <p>You requested to sign in to your Hirelytics account. Click the button below to sign in:</p>
          
          <p style="text-align: center; margin: 30px 0;">
            <a href="${magicLink}" class="button">Sign In to Hirelytics</a>
          </p>
          
          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background: #f1f5f9; padding: 10px; border-radius: 4px; font-family: monospace;">
            ${magicLink}
          </p>
        </div>
        
        <div class="warning">
          <strong>Security Notice:</strong> This link will expire in 15 minutes and can only be used once. 
          If you didn't request this sign-in link, you can safely ignore this email.
        </div>
        
        <div class="footer">
          <p>This email was sent to ${userEmail}</p>
          <p>&copy; 2024 Hirelytics. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Sign in to Hirelytics
    
    Hello,
    
    You requested to sign in to your Hirelytics account. Click the link below to sign in:
    
    ${magicLink}
    
    This link will expire in 15 minutes and can only be used once.
    
    If you didn't request this sign-in link, you can safely ignore this email.
    
    This email was sent to ${userEmail}
    
    © 2024 Hirelytics. All rights reserved.
  `;

  return { subject, html, text };
}

// OTP email template
export function createOTPEmail(
  otp: string,
  userEmail: string,
  purpose: 'login' | 'verification' = 'login'
): EmailTemplate {
  const subject =
    purpose === 'login' ? 'Your Hirelytics sign-in code' : 'Verify your Hirelytics account';

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
        .content { background: #f8fafc; padding: 30px; border-radius: 8px; margin: 20px 0; }
        .otp-code { font-size: 32px; font-weight: bold; text-align: center; background: white; padding: 20px; border-radius: 8px; letter-spacing: 8px; color: #2563eb; border: 2px solid #e5e7eb; }
        .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #6b7280; }
        .warning { background: #fef3cd; border: 1px solid #fbbf24; padding: 15px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">Hirelytics</div>
        </div>
        
        <div class="content">
          <h2>${purpose === 'login' ? 'Your sign-in code' : 'Verify your account'}</h2>
          <p>Hello,</p>
          <p>${
            purpose === 'login'
              ? 'Use the following code to sign in to your Hirelytics account:'
              : 'Use the following code to verify your Hirelytics account:'
          }</p>
          
          <div class="otp-code">${otp}</div>
          
          <p style="text-align: center; margin-top: 20px;">
            Enter this code in the ${purpose === 'login' ? 'sign-in' : 'verification'} form to continue.
          </p>
        </div>
        
        <div class="warning">
          <strong>Security Notice:</strong> This code will expire in 10 minutes. 
          If you didn't request this code, you can safely ignore this email.
        </div>
        
        <div class="footer">
          <p>This email was sent to ${userEmail}</p>
          <p>&copy; 2024 Hirelytics. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    ${subject}
    
    Hello,
    
    ${
      purpose === 'login'
        ? 'Use the following code to sign in to your Hirelytics account:'
        : 'Use the following code to verify your Hirelytics account:'
    }
    
    ${otp}
    
    Enter this code in the ${purpose === 'login' ? 'sign-in' : 'verification'} form to continue.
    
    This code will expire in 10 minutes.
    
    If you didn't request this code, you can safely ignore this email.
    
    This email was sent to ${userEmail}
    
    © 2024 Hirelytics. All rights reserved.
  `;

  return { subject, html, text };
}

// Welcome email template
export function createWelcomeEmail(userName: string, userEmail: string): EmailTemplate {
  const subject = 'Welcome to Hirelytics!';

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to Hirelytics</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
        .content { background: #f8fafc; padding: 30px; border-radius: 8px; margin: 20px 0; }
        .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
        .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #6b7280; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">Hirelytics</div>
        </div>
        
        <div class="content">
          <h2>Welcome to Hirelytics, ${userName}!</h2>
          <p>Thank you for joining Hirelytics. We're excited to have you on board!</p>
          
          <p>With Hirelytics, you can:</p>
          <ul>
            <li>Find and apply for amazing job opportunities</li>
            <li>Track your application progress</li>
            <li>Connect with top employers</li>
            <li>Build your professional profile</li>
          </ul>
          
          <p style="text-align: center; margin: 30px 0;">
            <a href="${env.APP_URL}/dashboard" class="button">Get Started</a>
          </p>
        </div>
        
        <div class="footer">
          <p>This email was sent to ${userEmail}</p>
          <p>&copy; 2024 Hirelytics. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Welcome to Hirelytics, ${userName}!
    
    Thank you for joining Hirelytics. We're excited to have you on board!
    
    With Hirelytics, you can:
    - Find and apply for amazing job opportunities
    - Track your application progress
    - Connect with top employers
    - Build your professional profile
    
    Get started: ${env.APP_URL}/dashboard
    
    This email was sent to ${userEmail}
    
    © 2024 Hirelytics. All rights reserved.
  `;

  return { subject, html, text };
}

// Send email function
export async function sendEmail(to: string, template: EmailTemplate): Promise<boolean> {
  try {
    await transporter.sendMail({
      from: `"Hirelytics" <${env.EMAIL_FROM}>`,
      to,
      subject: template.subject,
      text: template.text,
      html: template.html,
    });

    return true;
  } catch (error) {
    console.error('Failed to send email:', error);
    return false;
  }
}

// Convenience functions
export async function sendMagicLinkEmail(email: string, magicLink: string): Promise<boolean> {
  const template = createMagicLinkEmail(magicLink, email);
  return sendEmail(email, template);
}

export async function sendOTPEmail(
  email: string,
  otp: string,
  purpose: 'login' | 'verification' = 'login'
): Promise<boolean> {
  const template = createOTPEmail(otp, email, purpose);
  return sendEmail(email, template);
}

export async function sendWelcomeEmail(email: string, userName: string): Promise<boolean> {
  const template = createWelcomeEmail(userName, email);
  return sendEmail(email, template);
}
