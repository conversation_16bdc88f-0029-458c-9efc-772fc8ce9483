import { createCallerFactory, createTRPCRouter } from '@/server/api/trpc';

import { authRouter } from './routers/auth-router';
import { jobRouter } from './routers/job.router';
import { jobApplicationRouter } from './routers/job-application.router';
import { organizationRouter } from './routers/organization.router';
import { organizationMemberRouter } from './routers/organization-member.router';
import { userRouter } from './routers/user.router';

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  auth: authRouter,
  organization: organizationRouter,
  user: userRouter,
  job: jobRouter,
  organizationMember: organizationMemberRouter,
  jobApplication: jobApplicationRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
