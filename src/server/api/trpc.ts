/**
 * YOU PROBABLY DON'T NEED TO EDIT THIS FILE, UNLESS:
 * 1. You want to modify request context (see Part 1).
 * 2. You want to create a new middleware or type of procedure (see Part 3).
 *
 * TL;DR - This is where all the tRPC server stuff is created and plugged in. The pieces you will
 * need to use are documented accordingly near the end.
 */
import { initTRPC } from '@trpc/server';
import superjson from 'superjson';
import { ZodError } from 'zod';

import dbConnect from '@/db/mongoose';

/**
 * 1. CONTEXT
 *
 * This section defines the "contexts" that are available in the backend API.
 *
 * These allow you to access things when processing a request, like the database, the session, etc.
 *
 * This helper generates the "internals" for a tRPC context. The API handler and RSC clients each
 * wrap this and provides the required context.
 *
 * @see https://trpc.io/docs/server/context
 */
export const createTRPCContext = async (opts: { headers: Headers }) => {
  // Connect to the database
  const db = await dbConnect();

  // Extract cookies from headers
  const cookieHeader = opts.headers.get('cookie') || '';
  const cookies = parseCookies(cookieHeader);

  return {
    db,
    cookies,
    ...opts,
  };
};

// Helper function to parse cookies from header
function parseCookies(cookieHeader: string): Record<string, string> {
  const cookies: Record<string, string> = {};

  if (!cookieHeader) return cookies;

  cookieHeader.split(';').forEach((cookie) => {
    const [name, ...rest] = cookie.trim().split('=');
    if (name && rest.length > 0) {
      cookies[name] = decodeURIComponent(rest.join('='));
    }
  });

  return cookies;
}

/**
 * 2. INITIALIZATION
 *
 * This is where the tRPC API is initialized, connecting the context and transformer. We also parse
 * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation
 * errors on the backend.
 */
const t = initTRPC.context<typeof createTRPCContext>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError: error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

/**
 * Create a server-side caller.
 *
 * @see https://trpc.io/docs/server/server-side-calls
 */
export const createCallerFactory = t.createCallerFactory;

/**
 * 3. ROUTER & PROCEDURE (THE IMPORTANT BIT)
 *
 * These are the pieces you use to build your tRPC API. You should import these a lot in the
 * "/src/server/api/routers" directory.
 */

/**
 * This is how you create new routers and sub-routers in your tRPC API.
 *
 * @see https://trpc.io/docs/router
 */
export const createTRPCRouter = t.router;

/**
 * Middleware for timing procedure execution and adding an artificial delay in development.
 *
 * You can remove this if you don't like it, but it can help catch unwaterfalls by simulating
 * network latency that would occur in production but not in local development.
 */
const timingMiddleware = t.middleware(async ({ next, path }) => {
  const start = Date.now();

  if (t._config.isDev) {
    // artificial delay in dev
    const waitMs = Math.floor(Math.random() * 400) + 100;
    await new Promise((resolve) => setTimeout(resolve, waitMs));
  }

  const result = await next();

  const end = Date.now();
  console.log(`[TRPC] ${path} took ${end - start}ms to execute`);

  return result;
});

/**
 * Session middleware that enforces user authentication
 */
const sessionMiddleware = t.middleware(async ({ ctx, next }) => {
  // Try to get token from cookies first, then from Authorization header
  let token = ctx.cookies.accessToken;

  if (!token) {
    const authHeader = ctx.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.replace('Bearer ', '');
    }
  }

  if (!token) {
    throw new Error('UNAUTHORIZED: You must be logged in to access this resource');
  }

  try {
    // Import JWT verification function
    const jwt = await import('jsonwebtoken');
    const JWT_ACCESS_SECRET = process.env.JWT_ACCESS_SECRET || 'your-access-secret-key';

    const decoded = jwt.verify(token, JWT_ACCESS_SECRET) as {
      userId: string;
      email: string;
      role: string;
      type: string;
    };

    if (decoded.type !== 'access') {
      throw new Error('Invalid token type');
    }

    const user = {
      id: decoded.userId,
      email: decoded.email,
      role: decoded.role,
    };

    console.log(`[TRPC] User authenticated: ${user.id}`);

    return next({
      ctx: {
        ...ctx,
        user,
      },
    });
  } catch (error) {
    throw new Error('UNAUTHORIZED: Invalid or expired token', error as Error);
  }
});

/**
 * Public (unauthenticated) procedure
 *
 * This is the base piece you use to build new queries and mutations on your tRPC API. It does not
 * guarantee that a user querying is authorized, but you can still access user session data if they
 * are logged in.
 */
export const publicProcedure = t.procedure.use(timingMiddleware);

/**
 * Role-based authorization middleware
 */
const roleMiddleware = (allowedRoles: string[]) =>
  t.middleware(async ({ ctx, next }) => {
    const authHeader = ctx.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('UNAUTHORIZED: You must be logged in to access this resource');
    }

    // Mock user for development - replace with proper JWT validation
    const mockUser = {
      id: 'user_123',
      email: '<EMAIL>',
      role: 'admin' as const,
    };

    if (!allowedRoles.includes(mockUser.role)) {
      throw new Error('FORBIDDEN: Insufficient permissions');
    }

    return next({
      ctx: {
        ...ctx,
        user: mockUser,
      },
    });
  });

/**
 * Protected procedure that requires authentication
 *
 * This procedure ensures that the user is authenticated before allowing access to the endpoint.
 * Use this for any endpoints that require a logged-in user.
 */
export const protectedProcedure = publicProcedure.use(sessionMiddleware);

/**
 * Admin-only procedure
 */
export const adminProcedure = publicProcedure.use(roleMiddleware(['admin']));

/**
 * Recruiter and admin procedure
 */
export const recruiterProcedure = publicProcedure.use(roleMiddleware(['admin', 'recruiter']));

/**
 * HR, recruiter and admin procedure
 */
export const hrProcedure = publicProcedure.use(roleMiddleware(['admin', 'recruiter', 'hr']));
