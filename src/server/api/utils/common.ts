import { TRPCError } from '@trpc/server';
import { z } from 'zod';

/**
 * Common pagination schema
 */
export const PaginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export type PaginationInput = z.infer<typeof PaginationSchema>;

/**
 * Pagination response type
 */
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * Create pagination response
 */
export function createPaginatedResponse<T>(
  data: T[],
  total: number,
  page: number,
  limit: number
): PaginatedResponse<T> {
  const totalPages = Math.ceil(total / limit);

  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

/**
 * Calculate skip value for pagination
 */
export function calculateSkip(page: number, limit: number): number {
  return (page - 1) * limit;
}

/**
 * Common search schema
 */
export const SearchSchema = z.object({
  query: z.string().optional(),
  filters: z.record(z.unknown()).optional(),
});

export type SearchInput = z.infer<typeof SearchSchema>;

/**
 * Soft delete schema
 */
export const SoftDeleteSchema = z.object({
  isDeleted: z.boolean().default(false),
  deletedAt: z.date().optional(),
  deletedBy: z.string().optional(),
});

/**
 * Common ID schema
 */
export const IdSchema = z.object({
  id: z.string().min(1, 'ID is required'),
});

/**
 * Bulk operation schema
 */
export const BulkOperationSchema = z.object({
  ids: z.array(z.string().min(1)).min(1, 'At least one ID is required'),
});

/**
 * Status update schema
 */
export const StatusUpdateSchema = z.object({
  status: z.string().min(1, 'Status is required'),
});

/**
 * Error handling utilities
 */
export class ApiError extends Error {
  constructor(
    public code: string,
    message: string,
    public statusCode: number = 400
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export function handleApiError(error: unknown): never {
  if (error instanceof ApiError) {
    throw new TRPCError({
      code: error.code as any,
      message: error.message,
    });
  }

  if (error instanceof Error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: error.message,
    });
  }

  throw new TRPCError({
    code: 'INTERNAL_SERVER_ERROR',
    message: 'An unexpected error occurred',
  });
}

/**
 * Validation utilities
 */
export function validateObjectId(id: string): boolean {
  return /^[0-9a-fA-F]{24}$/.test(id);
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Common filters for database queries
 */
export interface CommonFilters {
  isDeleted?: boolean;
  status?: string;
  createdAt?: {
    gte?: Date;
    lte?: Date;
  };
  updatedAt?: {
    gte?: Date;
    lte?: Date;
  };
}

/**
 * Build MongoDB filter object
 */
export function buildMongoFilter(filters: Record<string, any> = {}): Record<string, any> {
  const mongoFilter: Record<string, any> = {};

  // Always exclude soft-deleted items by default
  mongoFilter.isDeleted = { $ne: true };

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (key === 'search' && typeof value === 'string') {
        // Text search across multiple fields
        mongoFilter.$or = [
          { name: { $regex: value, $options: 'i' } },
          { title: { $regex: value, $options: 'i' } },
          { description: { $regex: value, $options: 'i' } },
          { email: { $regex: value, $options: 'i' } },
        ];
      } else if (key === 'dateRange' && typeof value === 'object') {
        if (value.start)
          mongoFilter.createdAt = { ...mongoFilter.createdAt, $gte: new Date(value.start) };
        if (value.end)
          mongoFilter.createdAt = { ...mongoFilter.createdAt, $lte: new Date(value.end) };
      } else {
        mongoFilter[key] = value;
      }
    }
  });

  return mongoFilter;
}

/**
 * Build MongoDB sort object
 */
export function buildMongoSort(
  sortBy?: string,
  sortOrder: 'asc' | 'desc' = 'desc'
): Record<string, 1 | -1> {
  if (!sortBy) {
    return { createdAt: -1 }; // Default sort by creation date descending
  }

  return { [sortBy]: sortOrder === 'asc' ? 1 : -1 };
}

/**
 * Soft delete utility
 */
export async function softDelete(model: any, id: string, deletedBy?: string): Promise<any> {
  const updateData: any = {
    isDeleted: true,
    deletedAt: new Date(),
  };

  if (deletedBy) {
    updateData.deletedBy = deletedBy;
  }

  const result = await model.findByIdAndUpdate(id, updateData, { new: true });

  if (!result) {
    throw new ApiError('NOT_FOUND', 'Resource not found');
  }

  return result;
}

/**
 * Restore soft deleted item
 */
export async function restoreDeleted(model: any, id: string): Promise<any> {
  const result = await model.findByIdAndUpdate(
    id,
    {
      $unset: {
        isDeleted: 1,
        deletedAt: 1,
        deletedBy: 1,
      },
    },
    { new: true }
  );

  if (!result) {
    throw new ApiError('NOT_FOUND', 'Resource not found');
  }

  return result;
}

/**
 * Check if user has permission
 */
export function hasPermission(userRole: string, requiredRoles: string[]): boolean {
  return requiredRoles.includes(userRole);
}

/**
 * Role hierarchy for permission checking
 */
export const ROLE_HIERARCHY = {
  admin: 5,
  recruiter: 4,
  hr: 3,
  interviewer: 2,
  candidate: 1,
};

/**
 * Check if user role has sufficient level
 */
export function hasRoleLevel(userRole: string, minimumRole: string): boolean {
  const userLevel = ROLE_HIERARCHY[userRole as keyof typeof ROLE_HIERARCHY] || 0;
  const requiredLevel = ROLE_HIERARCHY[minimumRole as keyof typeof ROLE_HIERARCHY] || 0;

  return userLevel >= requiredLevel;
}
