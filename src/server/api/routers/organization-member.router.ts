import { TRPCError } from '@trpc/server';
import { z } from 'zod';

import {
  CreateOrganizationMemberSchema,
  InviteMemberSchema,
  MemberRoleSchema,
  MemberStatusSchema,
  UpdateOrganizationMemberSchema,
} from '@/@types/organization-member.types';
import { Organization, OrganizationMember, User } from '@/db/models';

import { createTRPCRouter, protectedProcedure } from '../trpc';
import {
  buildMongoFilter,
  buildMongoSort,
  calculateSkip,
  createPaginatedResponse,
  handleApiError,
  IdSchema,
  PaginationInput,
  PaginationSchema,
  SearchSchema,
  softDelete,
} from '../utils/common';

export const organizationMemberRouter = createTRPCRouter({
  /**
   * Add member to organization
   */
  addMember: protectedProcedure
    .input(CreateOrganizationMemberSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Check if current user has permission to add members
        const currentMember = await OrganizationMember.findOne({
          organizationId: input.organizationId,
          userId: ctx.user.id,
          status: 'active',
          isDeleted: { $ne: true },
        });

        if (!currentMember && ctx.user.role !== 'admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You are not a member of this organization',
          });
        }

        if (
          currentMember &&
          !currentMember.permissions.canManageMembers &&
          ctx.user.role !== 'admin'
        ) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to add members',
          });
        }

        // Verify the user exists
        const user = await User.findOne({
          $or: [{ userId: input.userId }, { _id: input.userId }],
          isDeleted: { $ne: true },
        });

        if (!user) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found',
          });
        }

        const memberData = {
          ...input,
          userId: user.userId,
          invitedBy: ctx.user.id,
        };

        const member = await OrganizationMember.createMember(memberData);

        // Update organization member count
        await Organization.findOneAndUpdate(
          { organizationId: input.organizationId },
          { $inc: { memberCount: 1 } }
        );

        return member;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Invite member to organization
   */
  inviteMember: protectedProcedure.input(InviteMemberSchema).mutation(async ({ input, ctx }) => {
    try {
      // Check if current user has permission to invite members
      const currentMember = await OrganizationMember.findOne({
        organizationId: input.organizationId,
        userId: ctx.user.id,
        status: 'active',
        isDeleted: { $ne: true },
      });

      if (!currentMember && ctx.user.role !== 'admin') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You are not a member of this organization',
        });
      }

      if (
        currentMember &&
        !currentMember.permissions.canInviteMembers &&
        ctx.user.role !== 'admin'
      ) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to invite members',
        });
      }

      const inviteData = {
        ...input,
        invitedBy: ctx.user.id,
      };

      const invitation = await OrganizationMember.inviteMember(inviteData);

      return invitation;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Get member by ID
   */
  getById: protectedProcedure.input(IdSchema).query(async ({ input, ctx }) => {
    try {
      const member = await OrganizationMember.findOne({
        $or: [{ memberId: input.id }, { _id: input.id }],
        isDeleted: { $ne: true },
      });

      if (!member) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Member not found',
        });
      }

      // Check if user has permission to view this member
      const currentMember = await OrganizationMember.findOne({
        organizationId: member.organizationId,
        userId: ctx.user.id,
        status: 'active',
        isDeleted: { $ne: true },
      });

      if (!currentMember && ctx.user.role !== 'admin') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to view this member',
        });
      }

      return member;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * List organization members with pagination
   */
  listByOrganization: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        pagination: PaginationSchema.optional(),
        search: SearchSchema.optional(),
        filters: z
          .object({
            role: MemberRoleSchema.optional(),
            status: MemberStatusSchema.optional(),
            invitationStatus: z.enum(['pending', 'accepted', 'declined', 'expired']).optional(),
          })
          .optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        // Check if user has permission to view members
        const currentMember = await OrganizationMember.findOne({
          organizationId: input.organizationId,
          userId: ctx.user.id,
          status: 'active',
          isDeleted: { $ne: true },
        });

        if (!currentMember && ctx.user.role !== 'admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to view organization members',
          });
        }

        const { pagination = {}, search = {}, filters = {} } = input;
        const { page = 1, limit = 10, sortBy, sortOrder = 'desc' } = pagination as PaginationInput;

        // Build filter object
        const mongoFilter = buildMongoFilter({
          ...filters,
          organizationId: input.organizationId,
          search: search.query,
        });

        const mongoSort = buildMongoSort(sortBy, sortOrder);
        const skip = calculateSkip(page, limit);

        const [members, total] = await Promise.all([
          OrganizationMember.find(mongoFilter).sort(mongoSort).skip(skip).limit(limit).lean(),
          OrganizationMember.countDocuments(mongoFilter),
        ]);

        return createPaginatedResponse(members, total, page, limit);
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Update member role and permissions
   */
  updateMember: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        data: UpdateOrganizationMemberSchema,
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const member = await OrganizationMember.findOne({
          $or: [{ memberId: input.id }, { _id: input.id }],
          isDeleted: { $ne: true },
        });

        if (!member) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Member not found',
          });
        }

        // Check if current user has permission to update members
        const currentMember = await OrganizationMember.findOne({
          organizationId: member.organizationId,
          userId: ctx.user.id,
          status: 'active',
          isDeleted: { $ne: true },
        });

        if (!currentMember && ctx.user.role !== 'admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update this member',
          });
        }

        if (
          currentMember &&
          !currentMember.permissions.canManageMembers &&
          ctx.user.role !== 'admin'
        ) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update members',
          });
        }

        // Prevent users from updating their own role (except admins)
        if (member.userId === ctx.user.id && ctx.user.role !== 'admin' && input.data.role) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You cannot update your own role',
          });
        }

        const updatedMember = await OrganizationMember.findByIdAndUpdate(
          member._id,
          { ...input.data, updatedAt: new Date() },
          { new: true, runValidators: true }
        );

        return updatedMember;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Remove member from organization
   */
  removeMember: protectedProcedure.input(IdSchema).mutation(async ({ input, ctx }) => {
    try {
      const member = await OrganizationMember.findOne({
        $or: [{ memberId: input.id }, { _id: input.id }],
        isDeleted: { $ne: true },
      });

      if (!member) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Member not found',
        });
      }

      // Check if current user has permission to remove members
      const currentMember = await OrganizationMember.findOne({
        organizationId: member.organizationId,
        userId: ctx.user.id,
        status: 'active',
        isDeleted: { $ne: true },
      });

      const canRemove =
        member.userId === ctx.user.id || // Users can remove themselves
        ctx.user.role === 'admin' ||
        (currentMember && currentMember.permissions.canRemoveMembers);

      if (!canRemove) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to remove this member',
        });
      }

      // Prevent removing organization owner
      const organization = await Organization.findOne({
        organizationId: member.organizationId,
        isDeleted: { $ne: true },
      });

      if (organization && organization.ownerId === member.userId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Cannot remove organization owner',
        });
      }

      await softDelete(OrganizationMember, member._id?.toString(), ctx.user.id);

      // Update organization member count
      await Organization.findOneAndUpdate(
        { organizationId: member.organizationId },
        { $inc: { memberCount: -1 } }
      );

      return { success: true, message: 'Member removed successfully' };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Accept invitation
   */
  acceptInvitation: protectedProcedure
    .input(z.object({ token: z.string() }))
    .mutation(async ({ input, ctx }) => {
      try {
        const invitation = await OrganizationMember.findOne({
          invitationToken: input.token,
          invitationStatus: 'pending',
          isDeleted: { $ne: true },
        });

        if (!invitation) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invalid or expired invitation',
          });
        }

        // Check if invitation has expired
        if (invitation.invitationExpires && invitation.invitationExpires < new Date()) {
          await OrganizationMember.findByIdAndUpdate(invitation._id, {
            invitationStatus: 'expired',
          });
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invitation has expired',
          });
        }

        // Update invitation with user ID and accept it
        const updatedMember = await OrganizationMember.findByIdAndUpdate(
          invitation._id,
          {
            userId: ctx.user.id,
            invitationStatus: 'accepted',
            status: 'active',
            joinedAt: new Date(),
            $unset: { invitationToken: 1, invitationExpires: 1 },
          },
          { new: true }
        );

        // Update organization member count
        await Organization.findOneAndUpdate(
          { organizationId: invitation.organizationId },
          { $inc: { memberCount: 1 } }
        );

        return updatedMember;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Decline invitation
   */
  declineInvitation: protectedProcedure
    .input(z.object({ token: z.string() }))
    .mutation(async ({ input }) => {
      try {
        const invitation = await OrganizationMember.findOne({
          invitationToken: input.token,
          invitationStatus: 'pending',
          isDeleted: { $ne: true },
        });

        if (!invitation) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invalid or expired invitation',
          });
        }

        await OrganizationMember.findByIdAndUpdate(invitation._id, {
          invitationStatus: 'declined',
          $unset: { invitationToken: 1, invitationExpires: 1 },
        });

        return { success: true, message: 'Invitation declined' };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Get user's organization memberships
   */
  getMyMemberships: protectedProcedure
    .input(PaginationSchema.optional())
    .query(async ({ input = {}, ctx }) => {
      try {
        const { page = 1, limit = 10, sortBy, sortOrder = 'desc' } = input;

        const mongoFilter = {
          userId: ctx.user.id,
          isDeleted: { $ne: true },
        };

        const mongoSort = buildMongoSort(sortBy, sortOrder);
        const skip = calculateSkip(page, limit);

        const [memberships, total] = await Promise.all([
          OrganizationMember.find(mongoFilter).sort(mongoSort).skip(skip).limit(limit).lean(),
          OrganizationMember.countDocuments(mongoFilter),
        ]);

        return createPaginatedResponse(memberships, total, page, limit);
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Get pending invitations for current user
   */
  getPendingInvitations: protectedProcedure.query(async ({ ctx }) => {
    try {
      // Find invitations by email (for cases where user wasn't registered when invited)
      const user = await User.findOne({ userId: ctx.user.id });
      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      const invitations = await OrganizationMember.find({
        $or: [
          { userId: ctx.user.id },
          { userId: { $regex: `^pending_.*_${user.email}$` } }, // For email-based invitations
        ],
        invitationStatus: 'pending',
        isDeleted: { $ne: true },
      }).lean();

      return invitations;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Bulk update member roles
   */
  bulkUpdateRoles: protectedProcedure
    .input(
      z.object({
        memberIds: z.array(z.string()).min(1),
        role: MemberRoleSchema,
        organizationId: z.string(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Check if current user has permission to manage members
        const currentMember = await OrganizationMember.findOne({
          organizationId: input.organizationId,
          userId: ctx.user.id,
          status: 'active',
          isDeleted: { $ne: true },
        });

        if (!currentMember && ctx.user.role !== 'admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update members in this organization',
          });
        }

        if (
          currentMember &&
          !currentMember.permissions.canManageMembers &&
          ctx.user.role !== 'admin'
        ) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update member roles',
          });
        }

        const result = await OrganizationMember.updateMany(
          {
            _id: { $in: input.memberIds },
            organizationId: input.organizationId,
            isDeleted: { $ne: true },
            userId: { $ne: ctx.user.id }, // Prevent updating own role
          },
          {
            role: input.role,
            updatedAt: new Date(),
          }
        );

        return {
          success: true,
          message: `Updated ${result.modifiedCount} members`,
          modifiedCount: result.modifiedCount,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Get organization member statistics
   */
  getStats: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        // Check if user has permission to view stats
        const currentMember = await OrganizationMember.findOne({
          organizationId: input.organizationId,
          userId: ctx.user.id,
          status: 'active',
          isDeleted: { $ne: true },
        });

        if (!currentMember && ctx.user.role !== 'admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to view organization statistics',
          });
        }

        const [totalMembers, activeMembers, pendingInvitations, roleDistribution] =
          await Promise.all([
            OrganizationMember.countDocuments({
              organizationId: input.organizationId,
              isDeleted: { $ne: true },
            }),
            OrganizationMember.countDocuments({
              organizationId: input.organizationId,
              status: 'active',
              isDeleted: { $ne: true },
            }),
            OrganizationMember.countDocuments({
              organizationId: input.organizationId,
              invitationStatus: 'pending',
              isDeleted: { $ne: true },
            }),
            OrganizationMember.aggregate([
              {
                $match: {
                  organizationId: input.organizationId,
                  isDeleted: { $ne: true },
                },
              },
              {
                $group: {
                  _id: '$role',
                  count: { $sum: 1 },
                },
              },
            ]),
          ]);

        const roles = roleDistribution.reduce(
          (acc, item) => {
            acc[item._id] = item.count;
            return acc;
          },
          {} as Record<string, number>
        );

        return {
          totalMembers,
          activeMembers,
          pendingInvitations,
          roleDistribution: roles,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),
});
