import { TRPCError } from '@trpc/server';
import { z } from 'zod';

import {
  ChangePasswordSchema,
  CreateUserSchema,
  UpdateUserSchema,
  UserRoleSchema,
  UserStatusSchema,
} from '@/@types/user.types';
import { User } from '@/db/models';

import { adminProcedure, createTRPCRouter, protectedProcedure, publicProcedure } from '../trpc';
import {
  buildMongoFilter,
  buildMongoSort,
  calculateSkip,
  createPaginatedResponse,
  handleApiError,
  hasRoleLevel,
  IdSchema,
  PaginationInput,
  PaginationSchema,
  SearchSchema,
  softDelete,
} from '../utils/common';

export const userRouter = createTRPCRouter({
  /**
   * Create a new user (Admin only)
   */
  create: adminProcedure.input(CreateUserSchema).mutation(async ({ input }) => {
    try {
      const user = await User.createUser(input);
      return user;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Register a new candidate (Public)
   */
  register: publicProcedure
    .input(
      CreateUserSchema.extend({
        role: z.literal('candidate').default('candidate'),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const userData = {
          ...input,
          role: 'candidate' as const,
        };

        const user = await User.createUser(userData);
        return user;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Get user by ID
   */
  getById: protectedProcedure.input(IdSchema).query(async ({ input, ctx }) => {
    try {
      const user = await User.findOne({
        $or: [{ userId: input.id }, { _id: input.id }],
        isDeleted: { $ne: true },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      // Users can only view their own profile unless they're admin
      if (user.userId !== ctx.user.id && ctx.user.role !== 'admin') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to view this user',
        });
      }

      return user;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Get current user profile
   */
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    try {
      const user = await User.findOne({
        userId: ctx.user.id,
        isDeleted: { $ne: true },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      return user;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * List users with pagination and search
   */
  list: adminProcedure
    .input(
      z.object({
        pagination: PaginationSchema.optional(),
        search: SearchSchema.optional(),
        filters: z
          .object({
            role: UserRoleSchema.optional(),
            status: UserStatusSchema.optional(),
            isEmailVerified: z.boolean().optional(),
          })
          .optional(),
      })
    )
    .query(async ({ input }) => {
      try {
        const { pagination = {}, search = {}, filters = {} } = input;

        const { page = 1, limit = 10, sortBy, sortOrder = 'desc' } = pagination as PaginationInput;

        // Build filter object
        const mongoFilter = buildMongoFilter({
          ...filters,
          search: search.query,
        });

        // Build sort object
        const mongoSort = buildMongoSort(sortBy, sortOrder);

        // Execute query with pagination
        const skip = calculateSkip(page, limit);

        const [users, total] = await Promise.all([
          User.find(mongoFilter).sort(mongoSort).skip(skip).limit(limit).lean(),
          User.countDocuments(mongoFilter),
        ]);

        return createPaginatedResponse(users, total, page, limit);
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * List candidates (for recruiters and admins)
   */
  listCandidates: protectedProcedure
    .input(
      z.object({
        pagination: PaginationSchema.optional(),
        search: SearchSchema.optional(),
        filters: z
          .object({
            status: UserStatusSchema.optional(),
            isEmailVerified: z.boolean().optional(),
          })
          .optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        // Check if user has permission to view candidates
        if (!hasRoleLevel(ctx.user.role, 'recruiter')) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to view candidates',
          });
        }

        const { pagination = {}, search = {}, filters = {} } = input;
        const { page = 1, limit = 10, sortBy, sortOrder = 'desc' } = pagination as PaginationInput;

        // Build filter object for candidates only
        const mongoFilter = buildMongoFilter({
          ...filters,
          role: 'candidate',
          search: search.query,
        });

        const mongoSort = buildMongoSort(sortBy, sortOrder);
        const skip = calculateSkip(page, limit);

        const [users, total] = await Promise.all([
          User.find(mongoFilter).sort(mongoSort).skip(skip).limit(limit).lean(),
          User.countDocuments(mongoFilter),
        ]);

        return createPaginatedResponse(users, total, page, limit);
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Update user profile
   */
  update: protectedProcedure
    .input(
      z.object({
        id: z.string().optional(), // If not provided, updates current user
        data: UpdateUserSchema,
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const targetUserId = input.id || ctx.user.id;

        // Users can only update their own profile unless they're admin
        if (targetUserId !== ctx.user.id && ctx.user.role !== 'admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update this user',
          });
        }

        const user = await User.findOne({
          $or: [{ userId: targetUserId }, { _id: targetUserId }],
          isDeleted: { $ne: true },
        });

        if (!user) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found',
          });
        }

        const updatedUser = await User.findByIdAndUpdate(
          user._id,
          { ...input.data, updatedAt: new Date() },
          { new: true, runValidators: true }
        );

        return updatedUser;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Update user role (Admin only)
   */
  updateRole: adminProcedure
    .input(
      z.object({
        id: z.string(),
        role: UserRoleSchema,
      })
    )
    .mutation(async ({ input }) => {
      try {
        const user = await User.findByIdAndUpdate(
          input.id,
          { role: input.role, updatedAt: new Date() },
          { new: true, runValidators: true }
        );

        if (!user) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found',
          });
        }

        return user;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Update user status (Admin only)
   */
  updateStatus: adminProcedure
    .input(
      z.object({
        id: z.string(),
        status: UserStatusSchema,
      })
    )
    .mutation(async ({ input }) => {
      try {
        const user = await User.findByIdAndUpdate(
          input.id,
          { status: input.status, updatedAt: new Date() },
          { new: true, runValidators: true }
        );

        if (!user) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found',
          });
        }

        return user;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Change password
   */
  changePassword: protectedProcedure
    .input(ChangePasswordSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const user = await User.findOne({
          userId: ctx.user.id,
          isDeleted: { $ne: true },
        });

        if (!user) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found',
          });
        }

        // Verify current password
        const isValidPassword = await user.comparePassword(input.currentPassword);
        if (!isValidPassword) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Current password is incorrect',
          });
        }

        // Update password
        user.password = input.newPassword;
        await user.save();

        return { success: true, message: 'Password updated successfully' };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Soft delete user
   */
  delete: protectedProcedure.input(IdSchema).mutation(async ({ input, ctx }) => {
    try {
      const targetUserId = input.id;

      // Users can only delete their own account unless they're admin
      if (targetUserId !== ctx.user.id && ctx.user.role !== 'admin') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to delete this user',
        });
      }

      const user = await User.findOne({
        $or: [{ userId: targetUserId }, { _id: targetUserId }],
        isDeleted: { $ne: true },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      await softDelete(User, user._id, ctx.user.id);

      return { success: true, message: 'User deleted successfully' };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Bulk update user status (Admin only)
   */
  bulkUpdateStatus: adminProcedure
    .input(
      z.object({
        userIds: z.array(z.string()).min(1),
        status: UserStatusSchema,
      })
    )
    .mutation(async ({ input }) => {
      try {
        const result = await User.updateMany(
          { _id: { $in: input.userIds }, isDeleted: { $ne: true } },
          { status: input.status, updatedAt: new Date() }
        );

        return {
          success: true,
          message: `Updated ${result.modifiedCount} users`,
          modifiedCount: result.modifiedCount,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Get user statistics (Admin only)
   */
  getStats: adminProcedure.query(async () => {
    try {
      const [totalUsers, activeUsers, candidateCount, recruiterCount, adminCount, verifiedUsers] =
        await Promise.all([
          User.countDocuments({ isDeleted: { $ne: true } }),
          User.countDocuments({ status: 'active', isDeleted: { $ne: true } }),
          User.countDocuments({ role: 'candidate', isDeleted: { $ne: true } }),
          User.countDocuments({ role: 'recruiter', isDeleted: { $ne: true } }),
          User.countDocuments({ role: 'admin', isDeleted: { $ne: true } }),
          User.countDocuments({ isEmailVerified: true, isDeleted: { $ne: true } }),
        ]);

      return {
        totalUsers,
        activeUsers,
        roleDistribution: {
          candidates: candidateCount,
          recruiters: recruiterCount,
          admins: adminCount,
        },
        verifiedUsers,
        verificationRate: totalUsers > 0 ? (verifiedUsers / totalUsers) * 100 : 0,
      };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Search users by email or name (Admin and Recruiter only)
   */
  search: protectedProcedure
    .input(
      z.object({
        query: z.string().min(1),
        role: UserRoleSchema.optional(),
        limit: z.number().min(1).max(50).default(10),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        // Check if user has permission to search users
        if (!hasRoleLevel(ctx.user.role, 'recruiter')) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to search users',
          });
        }

        const filter: any = {
          isDeleted: { $ne: true },
          $or: [
            { firstName: { $regex: input.query, $options: 'i' } },
            { lastName: { $regex: input.query, $options: 'i' } },
            { email: { $regex: input.query, $options: 'i' } },
          ],
        };

        if (input.role) {
          filter.role = input.role;
        }

        const users = await User.find(filter)
          .limit(input.limit)
          .select('userId firstName lastName email role status avatar')
          .lean();

        return users;
      } catch (error) {
        handleApiError(error);
      }
    }),
});
