import { TRPCError } from '@trpc/server';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { z } from 'zod';

import { CreateUserSchema, IUser, LoginSchema } from '@/@types/user.types';
import { User } from '@/db/models';
import { env } from '@/env';

import { createTRPCRouter, protectedProcedure, publicProcedure } from '../trpc';
import { handleApiError } from '../utils/common';

// JWT configuration
const JWT_ACCESS_SECRET = env.JWT_ACCESS_SECRET;
const JWT_REFRESH_SECRET = env.JWT_REFRESH_SECRET;
const ACCESS_TOKEN_EXPIRES_IN = '15m'; // 15 minutes
const REFRESH_TOKEN_EXPIRES_IN = '7d'; // 7 days

// Cookie configuration
const COOKIE_OPTIONS = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
  path: '/',
};

const ACCESS_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 15 * 60, // 15 minutes in seconds
};

const REFRESH_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
};

// Token generation utilities
function generateAccessToken(userId: string, email: string, role: string) {
  return jwt.sign({ userId, email, role, type: 'access' }, JWT_ACCESS_SECRET, {
    expiresIn: ACCESS_TOKEN_EXPIRES_IN,
  });
}

function generateRefreshToken(userId: string) {
  return jwt.sign({ userId, type: 'refresh' }, JWT_REFRESH_SECRET, {
    expiresIn: REFRESH_TOKEN_EXPIRES_IN,
  });
}

function verifyRefreshToken(token: string) {
  try {
    return jwt.verify(token, JWT_REFRESH_SECRET) as {
      userId: string;
      type: string;
    };
  } catch (_error) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Invalid refresh token',
    });
  }
}

// Set authentication cookies (for server actions)
async function setAuthCookies(accessToken: string, refreshToken: string) {
  const cookieStore = await cookies();
  cookieStore.set('accessToken', accessToken, ACCESS_COOKIE_OPTIONS);
  cookieStore.set('refreshToken', refreshToken, REFRESH_COOKIE_OPTIONS);
}

// Clear authentication cookies (for server actions)
async function clearAuthCookies() {
  const cookieStore = await cookies();
  cookieStore.delete('accessToken');
  cookieStore.delete('refreshToken');
}

export const authRouter = createTRPCRouter({
  /**
   * User login
   */
  login: publicProcedure.input(LoginSchema).mutation(async ({ input }) => {
    try {
      const { user, error } = await User.authenticate(input.email, input.password);

      if (error || !user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: error || 'Invalid credentials',
        });
      }

      // Generate tokens
      const accessToken = generateAccessToken(user.userId, user.email, user.role);
      const refreshToken = generateRefreshToken(user.userId);

      // Set cookies
      setAuthCookies(accessToken, refreshToken);

      // Update last login
      await User.findByIdAndUpdate(user._id, { lastLoginAt: new Date() });

      return {
        user: {
          id: user.userId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          status: user.status,
          isEmailVerified: user.isEmailVerified,
        },
        success: true,
      };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * User registration (Candidate)
   */
  register: publicProcedure
    .input(
      CreateUserSchema.extend({
        role: z.literal('candidate').default('candidate'),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const userData = {
          ...input,
          role: 'candidate' as const,
        };

        const user = await User.createUser(userData);

        // Generate tokens
        const accessToken = generateAccessToken(user.userId, user.email, user.role);
        const refreshToken = generateRefreshToken(user.userId);

        // Set cookies
        setAuthCookies(accessToken, refreshToken);

        return {
          user: {
            id: user.userId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            status: user.status,
            isEmailVerified: user.isEmailVerified,
          },
          success: true,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Recruiter registration
   */
  registerRecruiter: publicProcedure
    .input(
      CreateUserSchema.extend({
        role: z.literal('recruiter').default('recruiter'),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const userData = {
          ...input,
          role: 'recruiter' as const,
        };

        const user = await User.createUser(userData);

        // Generate tokens
        const accessToken = generateAccessToken(user.userId, user.email, user.role);
        const refreshToken = generateRefreshToken(user.userId);

        // Set cookies
        setAuthCookies(accessToken, refreshToken);

        return {
          user: {
            id: user.userId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            status: user.status,
            isEmailVerified: user.isEmailVerified,
          },
          success: true,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Get current user profile
   */
  me: protectedProcedure.query(async ({ ctx }) => {
    try {
      const user = await User.findOne({
        userId: ctx.user.id,
        isDeleted: { $ne: true },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      return {
        id: user.userId,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
        isEmailVerified: user.isEmailVerified,
        phone: user.phone,
        avatar: user.avatar,
        timezone: user.timezone,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
      };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Logout
   */
  logout: protectedProcedure.mutation(async () => {
    try {
      // Clear authentication cookies
      clearAuthCookies();

      return { success: true, message: 'Logged out successfully' };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Refresh token
   */
  refreshToken: publicProcedure.mutation(async ({ ctx }) => {
    try {
      const refreshToken = ctx.cookies.refreshToken;

      if (!refreshToken) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'No refresh token provided',
        });
      }

      // Verify refresh token
      const decoded = verifyRefreshToken(refreshToken);

      // Get user from database
      const user = await User.findOne({
        userId: decoded.userId,
        isDeleted: { $ne: true },
      });

      if (!user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'User not found',
        });
      }

      // Generate new tokens
      const newAccessToken = generateAccessToken(user.userId, user.email, user.role);
      const newRefreshToken = generateRefreshToken(user.userId);

      // Set new cookies
      await setAuthCookies(newAccessToken, newRefreshToken);

      return {
        user: {
          id: user.userId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          status: user.status,
          isEmailVerified: user.isEmailVerified,
        },
        success: true,
      };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Request password reset
   */
  requestPasswordReset: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .mutation(async ({ input }) => {
      try {
        const user = await User.findByEmail(input.email);

        if (!user) {
          // Don't reveal if email exists or not for security
          return { success: true, message: 'If the email exists, a reset link has been sent' };
        }

        // In a real implementation, you would:
        // 1. Generate a secure reset token
        // 2. Save it to the user record with expiration
        // 3. Send an email with the reset link

        const resetToken = `reset_${Date.now()}_${user.userId}`;
        await User.findByIdAndUpdate(user._id, {
          passwordResetToken: resetToken,
          passwordResetExpires: new Date(Date.now() + 3600000), // 1 hour
        });

        return { success: true, message: 'If the email exists, a reset link has been sent' };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Reset password
   */
  resetPassword: publicProcedure
    .input(
      z.object({
        token: z.string(),
        newPassword: z.string().min(8),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const user = await User.findByResetToken(input.token);

        if (!user) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid or expired reset token',
          });
        }

        // Update password and clear reset token
        user.password = input.newPassword;
        user.passwordResetToken = undefined;
        user.passwordResetExpires = undefined;
        await user.save();

        return { success: true, message: 'Password reset successfully' };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Send magic link
   */
  sendMagicLink: publicProcedure
    .input(
      z.object({
        email: z.string().email(),
        purpose: z.enum(['login', 'verification', 'password-reset']).default('login'),
        redirectTo: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const { checkMagicLinkRateLimit, createSecureMagicLink } = await import('@/lib/magic-link');
        const { sendMagicLinkEmail } = await import('@/lib/email');

        // Check rate limiting
        const rateLimit = checkMagicLinkRateLimit(input.email);
        if (!rateLimit.allowed) {
          throw new TRPCError({
            code: 'TOO_MANY_REQUESTS',
            message: 'Too many magic link requests. Please try again later.',
          });
        }

        // For login and password reset, check if user exists
        if (input.purpose === 'login' || input.purpose === 'password-reset') {
          const user = await User.findOne({
            email: input.email.toLowerCase(),
            isDeleted: { $ne: true },
          });

          if (!user) {
            // Don't reveal if user exists or not for security
            return {
              success: true,
              message: 'If an account with this email exists, a magic link has been sent.',
            };
          }

          // Check if user account is active
          if (user.status !== 'active') {
            throw new TRPCError({
              code: 'FORBIDDEN',
              message: 'Your account is not active. Please contact support.',
            });
          }
        }

        // Generate magic link
        const { magicLink, expiresAt } = await createSecureMagicLink(
          input.email,
          input.purpose,
          input.redirectTo
        );

        // Send magic link email
        const emailSent = await sendMagicLinkEmail(input.email, magicLink);

        if (!emailSent) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'Failed to send magic link email',
          });
        }

        return {
          success: true,
          message: 'Magic link sent successfully',
          expiresAt,
          remainingAttempts: rateLimit.remainingAttempts,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Send OTP
   */
  sendOTP: publicProcedure
    .input(
      z.object({
        email: z.string().email(),
        purpose: z.enum(['login', 'verification', 'password-reset']).default('login'),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const { generateAndStoreOTP, checkOTPRateLimit } = await import('@/lib/otp');
        const { sendOTPEmail } = await import('@/lib/email');

        // Check rate limiting
        const rateLimit = checkOTPRateLimit(input.email);
        if (!rateLimit.allowed) {
          throw new TRPCError({
            code: 'TOO_MANY_REQUESTS',
            message: 'Too many OTP requests. Please try again later.',
          });
        }

        // For login and password reset, check if user exists
        if (input.purpose === 'login' || input.purpose === 'password-reset') {
          const user = await User.findOne({
            email: input.email.toLowerCase(),
            isDeleted: { $ne: true },
          });

          if (!user) {
            // Don't reveal if user exists or not for security
            return {
              success: true,
              message: 'If an account with this email exists, an OTP has been sent.',
            };
          }

          // Check if user account is active
          if (user.status !== 'active') {
            throw new TRPCError({
              code: 'FORBIDDEN',
              message: 'Your account is not active. Please contact support.',
            });
          }
        }

        // Generate and store OTP
        const { record, code } = await generateAndStoreOTP(input.email, input.purpose);

        // Send OTP email
        const emailSent = await sendOTPEmail(
          input.email,
          code,
          input.purpose === 'password-reset' ? 'verification' : input.purpose
        );

        if (!emailSent) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'Failed to send OTP email',
          });
        }

        return {
          success: true,
          message: 'OTP sent successfully',
          expiresAt: record.expiresAt,
          remainingAttempts: rateLimit.remainingAttempts,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Verify OTP
   */
  verifyOTP: publicProcedure
    .input(
      z.object({
        email: z.string().email(),
        code: z.string().length(6),
        purpose: z.enum(['login', 'verification', 'password-reset']).default('login'),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const { validateOTP } = await import('@/lib/otp');
        const { sendWelcomeEmail } = await import('@/lib/email');

        // Validate OTP
        const validation = validateOTP({
          email: input.email,
          code: input.code,
          purpose: input.purpose,
          deleteAfterVerification: true,
        });

        if (!validation.valid) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: validation.error || 'The OTP code is invalid or expired',
          });
        }

        // Handle different purposes
        switch (input.purpose) {
          case 'login': {
            // Find user
            const user = await User.findOne({
              email: input.email.toLowerCase(),
              isDeleted: { $ne: true },
            });

            if (!user) {
              throw new TRPCError({
                code: 'NOT_FOUND',
                message: 'No account found with this email address',
              });
            }

            if (user.status !== 'active') {
              throw new TRPCError({
                code: 'FORBIDDEN',
                message: 'Your account is not active. Please contact support.',
              });
            }

            // Update last login
            user.lastLoginAt = new Date();
            await user.save();

            // Generate JWT tokens
            const accessToken = generateAccessToken(user.userId, user.email, user.role);
            const refreshToken = generateRefreshToken(user.userId);

            // Set authentication cookies
            await setAuthCookies(accessToken, refreshToken);

            return {
              success: true,
              message: 'Login successful',
              user: {
                id: user.userId,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                status: user.status,
                isEmailVerified: user.isEmailVerified,
              },
            };
          }

          case 'verification': {
            // Find user
            const user = await User.findOne({
              email: input.email.toLowerCase(),
              isDeleted: { $ne: true },
            });

            if (!user) {
              throw new TRPCError({
                code: 'NOT_FOUND',
                message: 'No account found with this email address',
              });
            }

            // Verify email
            if (!user.isEmailVerified) {
              user.isEmailVerified = true;
              user.emailVerifiedAt = new Date();
              await user.save();

              // Send welcome email for new users
              if (user.createdAt && Date.now() - user.createdAt.getTime() < 24 * 60 * 60 * 1000) {
                await sendWelcomeEmail(user.email, `${user.firstName} ${user.lastName}`);
              }
            }

            return {
              success: true,
              message: 'Email verified successfully',
              user: {
                id: user.userId,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                status: user.status,
                isEmailVerified: user.isEmailVerified,
              },
            };
          }

          case 'password-reset': {
            // For password reset, return a temporary token that can be used to reset password
            const resetToken = jwt.sign(
              { email: input.email, purpose: 'password-reset', verified: true },
              JWT_ACCESS_SECRET,
              { expiresIn: '15m' }
            );

            return {
              success: true,
              message: 'OTP verified. You can now reset your password.',
              resetToken,
            };
          }

          default: {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: 'Invalid OTP purpose',
            });
          }
        }
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Get OAuth authorization URL
   */
  getOAuthUrl: publicProcedure
    .input(
      z.object({
        provider: z.enum(['google', 'microsoft']),
        redirectTo: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const { generateOAuthState, getOAuthAuthorizationUrl } = await import('@/lib/oauth');

        // Generate OAuth state
        const state = generateOAuthState(input.provider, input.redirectTo);

        // Get authorization URL
        const authUrl = getOAuthAuthorizationUrl(input.provider, state);

        return {
          success: true,
          authUrl,
          state,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Handle OAuth callback
   */
  handleOAuthCallback: publicProcedure
    .input(
      z.object({
        provider: z.enum(['google', 'microsoft']),
        code: z.string(),
        state: z.string(),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const {
          parseOAuthState,
          exchangeOAuthCode,
          fetchGoogleUserProfile,
          fetchMicrosoftUserProfile,
          normalizeGoogleUser,
          normalizeMicrosoftUser,
        } = await import('@/lib/oauth');

        // Parse and validate state
        const oauthState = parseOAuthState(input.state);
        if (!oauthState || oauthState.provider !== input.provider) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid OAuth state',
          });
        }

        // Exchange code for tokens
        const tokens = await exchangeOAuthCode(input.provider, input.code);

        // Fetch user profile
        let normalizedUser;
        if (input.provider === 'google') {
          const googleProfile = await fetchGoogleUserProfile(tokens.accessToken);
          normalizedUser = normalizeGoogleUser(googleProfile);
        } else {
          const microsoftProfile = await fetchMicrosoftUserProfile(tokens.accessToken);
          normalizedUser = normalizeMicrosoftUser(microsoftProfile);
        }

        // Find or create user
        let user: IUser | null = await User.findOne({
          $or: [
            { email: normalizedUser.email },
            { [`oauthProviders.${input.provider}.id`]: normalizedUser.providerId },
          ],
          isDeleted: { $ne: true },
        });

        if (user) {
          // Update existing user with OAuth info
          if (!user.oauthProviders) {
            user.oauthProviders = {};
          }

          user.oauthProviders[input.provider] = {
            id: normalizedUser.providerId,
            email: normalizedUser.email,
            connectedAt: new Date(),
          };

          // Update user info if not set
          if (!user.avatar && normalizedUser.avatar) {
            user.avatar = normalizedUser.avatar;
          }

          if (!user.isEmailVerified && normalizedUser.isEmailVerified) {
            user.isEmailVerified = true;
            user.emailVerifiedAt = new Date();
          }

          user.lastLoginAt = new Date();
          await user.save();
        } else {
          // Create new user
          const userData = {
            email: normalizedUser.email,
            firstName: normalizedUser.firstName,
            lastName: normalizedUser.lastName,
            avatar: normalizedUser.avatar,
            isEmailVerified: normalizedUser.isEmailVerified,
            emailVerifiedAt: normalizedUser.isEmailVerified ? new Date() : undefined,
            role: 'candidate' as const,
            status: 'active' as const,
            timezone: 'UTC',
            acceptedTerms: true,
            marketingConsent: false,
            oauthProviders: {
              [input.provider]: {
                id: normalizedUser.providerId,
                email: normalizedUser.email,
                connectedAt: new Date(),
              },
            },
            lastLoginAt: new Date(),
          };

          user = await User.createUser(userData);
        }

        // Ensure user is not null
        if (!user) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'Failed to create or find user',
          });
        }

        // Generate JWT tokens
        const accessToken = generateAccessToken(user.userId, user.email, user.role);
        const refreshToken = generateRefreshToken(user.userId);

        // Set authentication cookies
        await setAuthCookies(accessToken, refreshToken);

        return {
          success: true,
          user: {
            id: user.userId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            status: user.status,
            isEmailVerified: user.isEmailVerified,
          },
          redirectTo: oauthState.redirectTo || '/dashboard',
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Verify magic link token
   */
  verifyMagicLink: publicProcedure
    .input(
      z.object({
        token: z.string(),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const { validateMagicLinkToken, markTokenAsUsed } = await import('@/lib/magic-link');
        const { sendWelcomeEmail } = await import('@/lib/email');

        // Validate magic link token
        const validation = validateMagicLinkToken(input.token);

        if (!validation.valid || !validation.payload) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid or expired magic link',
          });
        }

        const { email, purpose, redirectTo } = validation.payload;

        // Mark token as used
        markTokenAsUsed(input.token);

        // Handle different purposes
        switch (purpose) {
          case 'login': {
            // Find user
            const user = await User.findOne({
              email: email.toLowerCase(),
              isDeleted: { $ne: true },
            });

            if (!user) {
              throw new TRPCError({
                code: 'NOT_FOUND',
                message: 'User not found',
              });
            }

            if (user.status !== 'active') {
              throw new TRPCError({
                code: 'FORBIDDEN',
                message: 'Account inactive',
              });
            }

            // Update last login
            user.lastLoginAt = new Date();
            await user.save();

            // Generate JWT tokens
            const accessToken = generateAccessToken(user.userId, user.email, user.role);
            const refreshToken = generateRefreshToken(user.userId);

            // Set authentication cookies
            await setAuthCookies(accessToken, refreshToken);

            return {
              success: true,
              user: {
                id: user.userId,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                status: user.status,
                isEmailVerified: user.isEmailVerified,
              },
              redirectTo: redirectTo || '/dashboard',
            };
          }

          case 'verification': {
            // Find user
            const user = await User.findOne({
              email: email.toLowerCase(),
              isDeleted: { $ne: true },
            });

            if (!user) {
              throw new TRPCError({
                code: 'NOT_FOUND',
                message: 'User not found',
              });
            }

            // Verify email
            if (!user.isEmailVerified) {
              user.isEmailVerified = true;
              user.emailVerifiedAt = new Date();
              await user.save();

              // Send welcome email for new users
              if (user.createdAt && Date.now() - user.createdAt.getTime() < 24 * 60 * 60 * 1000) {
                await sendWelcomeEmail(user.email, `${user.firstName} ${user.lastName}`);
              }
            }

            // Generate JWT tokens
            const accessToken = generateAccessToken(user.userId, user.email, user.role);
            const refreshToken = generateRefreshToken(user.userId);

            // Set authentication cookies
            await setAuthCookies(accessToken, refreshToken);

            return {
              success: true,
              user: {
                id: user.userId,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                status: user.status,
                isEmailVerified: user.isEmailVerified,
              },
              redirectTo: redirectTo || '/dashboard',
              verified: true,
            };
          }

          case 'password-reset': {
            return {
              success: true,
              resetToken: input.token,
              email,
              message: 'Magic link verified. You can now reset your password.',
            };
          }

          default: {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: 'Invalid purpose',
            });
          }
        }
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Verify email (placeholder)
   */
  verifyEmail: publicProcedure
    .input(z.object({ token: z.string() }))
    .mutation(async ({ input }) => {
      try {
        // In a real implementation, you would validate the email verification token
        // For now, we'll just return success
        console.log('Email verification token:', input.token);
        return { success: true, message: 'Email verified successfully' };
      } catch (error) {
        handleApiError(error);
      }
    }),
});
