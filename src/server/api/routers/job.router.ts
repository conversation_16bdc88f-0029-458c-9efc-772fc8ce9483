import { TRPCError } from '@trpc/server';
import { z } from 'zod';

import {
  JobExperienceLevelSchema,
  JobStatusSchema,
  JobTypeSchema,
  UpdateJobSchema,
  WorkLocationSchema,
} from '@/@types/job.types';
import { Job, Organization, OrganizationMember } from '@/db/models';
import { CreateJobSchema } from '@/db/models/job.model';

import { createTRPCRouter, protectedProcedure, publicProcedure, recruiterProcedure } from '../trpc';
import {
  buildMongoFilter,
  buildMongoSort,
  calculateSkip,
  createPaginatedResponse,
  handleApiError,
  IdSchema,
  PaginationInput,
  PaginationSchema,
  SearchSchema,
  softDelete,
} from '../utils/common';

export const jobRouter = createTRPCRouter({
  /**
   * Create a new job
   */
  create: recruiterProcedure.input(CreateJobSchema).mutation(async ({ input, ctx }) => {
    try {
      // Check if user has permission to create jobs in this organization
      const member = await OrganizationMember.findOne({
        organizationId: input.organizationId,
        userId: ctx.user.id,
        status: 'active',
        isDeleted: { $ne: true },
      });

      if (!member && ctx.user.role !== 'admin') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to create jobs in this organization',
        });
      }

      // Check if member has job creation permissions
      if (member && !member.permissions.canCreateJobs && ctx.user.role !== 'admin') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to create jobs',
        });
      }

      const jobData = {
        ...input,
        postedBy: ctx.user.id,
      };

      const job = await Job.createJob(jobData);

      // Update organization job count
      await Organization.findOneAndUpdate(
        { organizationId: input.organizationId },
        { $inc: { jobCount: 1 } }
      );

      return job;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Get job by ID
   */
  getById: publicProcedure.input(IdSchema).query(async ({ input }) => {
    try {
      const job = await Job.findOne({
        $or: [{ jobId: input.id }, { _id: input.id }],
        isDeleted: { $ne: true },
      });

      if (!job) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Job not found',
        });
      }

      // Increment view count
      await Job.findByIdAndUpdate(job._id, { $inc: { viewCount: 1 } });

      return job;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Get job by slug
   */
  getBySlug: publicProcedure.input(z.object({ slug: z.string() })).query(async ({ input }) => {
    try {
      const job = await Job.findOne({
        slug: input.slug,
        isDeleted: { $ne: true },
      });

      if (!job) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Job not found',
        });
      }

      // Increment view count
      await Job.findByIdAndUpdate(job._id, { $inc: { viewCount: 1 } });

      return job;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * List jobs with pagination and search
   */
  list: publicProcedure
    .input(
      z.object({
        pagination: PaginationSchema.optional(),
        search: SearchSchema.optional(),
        filters: z
          .object({
            organizationId: z.string().optional(),
            status: JobStatusSchema.optional(),
            type: JobTypeSchema.optional(),
            experienceLevel: JobExperienceLevelSchema.optional(),
            workLocation: WorkLocationSchema.optional(),
            location: z.string().optional(),
            salaryMin: z.number().optional(),
            salaryMax: z.number().optional(),
            tags: z.array(z.string()).optional(),
            isActive: z.boolean().optional(),
          })
          .optional(),
      })
    )
    .query(async ({ input }) => {
      try {
        const { pagination = {}, search = {}, filters = {} } = input;
        const { page = 1, limit = 10, sortBy, sortOrder = 'desc' } = pagination as PaginationInput;

        // Build filter object
        const mongoFilter = buildMongoFilter({
          ...filters,
          search: search.query,
          // Only show published jobs for public access
          status: filters.status || 'published',
        });

        // Handle salary range filtering
        if (filters.salaryMin || filters.salaryMax) {
          mongoFilter['salaryRange.min'] = {};
          if (filters.salaryMin) mongoFilter['salaryRange.min'].$gte = filters.salaryMin;
          if (filters.salaryMax) mongoFilter['salaryRange.max'] = { $lte: filters.salaryMax };
        }

        // Handle tags filtering
        if (filters.tags && filters.tags.length > 0) {
          mongoFilter.tags = { $in: filters.tags };
        }

        // Handle location filtering
        if (filters.location) {
          mongoFilter.$or = [
            { 'location.city': { $regex: filters.location, $options: 'i' } },
            { 'location.state': { $regex: filters.location, $options: 'i' } },
            { 'location.country': { $regex: filters.location, $options: 'i' } },
          ];
        }

        // Build sort object
        const mongoSort = buildMongoSort(sortBy, sortOrder);

        // Execute query with pagination
        const skip = calculateSkip(page, limit);

        const [jobs, total] = await Promise.all([
          Job.find(mongoFilter).sort(mongoSort).skip(skip).limit(limit).lean(),
          Job.countDocuments(mongoFilter),
        ]);

        return createPaginatedResponse(jobs, total, page, limit);
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * List jobs for organization
   */
  listByOrganization: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        pagination: PaginationSchema.optional(),
        search: SearchSchema.optional(),
        filters: z
          .object({
            status: JobStatusSchema.optional(),
            type: JobTypeSchema.optional(),
            experienceLevel: JobExperienceLevelSchema.optional(),
            workLocation: WorkLocationSchema.optional(),
          })
          .optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        // Check if user has permission to view jobs in this organization
        const member = await OrganizationMember.findOne({
          organizationId: input.organizationId,
          userId: ctx.user.id,
          status: 'active',
          isDeleted: { $ne: true },
        });

        if (!member && ctx.user.role !== 'admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to view jobs in this organization',
          });
        }

        const { pagination = {}, search = {}, filters = {} } = input;
        const { page = 1, limit = 10, sortBy, sortOrder = 'desc' } = pagination as PaginationInput;

        // Build filter object
        const mongoFilter = buildMongoFilter({
          ...filters,
          organizationId: input.organizationId,
          search: search.query,
        });

        const mongoSort = buildMongoSort(sortBy, sortOrder);
        const skip = calculateSkip(page, limit);

        const [jobs, total] = await Promise.all([
          Job.find(mongoFilter).sort(mongoSort).skip(skip).limit(limit).lean(),
          Job.countDocuments(mongoFilter),
        ]);

        return createPaginatedResponse(jobs, total, page, limit);
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Update job
   */
  update: recruiterProcedure
    .input(
      z.object({
        id: z.string(),
        data: UpdateJobSchema,
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const job = await Job.findOne({
          $or: [{ jobId: input.id }, { _id: input.id }],
          isDeleted: { $ne: true },
        });

        if (!job) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Job not found',
          });
        }

        // Check if user has permission to update this job
        const member = await OrganizationMember.findOne({
          organizationId: job.organizationId,
          userId: ctx.user.id,
          status: 'active',
          isDeleted: { $ne: true },
        });

        const canUpdate =
          job.postedBy === ctx.user.id ||
          ctx.user.role === 'admin' ||
          (member && member.permissions.canEditJobs);

        if (!canUpdate) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update this job',
          });
        }

        const updatedJob = await Job.findByIdAndUpdate(
          job._id,
          { ...input.data, updatedAt: new Date() },
          { new: true, runValidators: true }
        );

        return updatedJob;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Update job status
   */
  updateStatus: recruiterProcedure
    .input(
      z.object({
        id: z.string(),
        status: JobStatusSchema,
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const job = await Job.findOne({
          $or: [{ jobId: input.id }, { _id: input.id }],
          isDeleted: { $ne: true },
        });

        if (!job) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Job not found',
          });
        }

        // Check permissions
        const member = await OrganizationMember.findOne({
          organizationId: job.organizationId,
          userId: ctx.user.id,
          status: 'active',
          isDeleted: { $ne: true },
        });

        const canUpdate =
          job.postedBy === ctx.user.id ||
          ctx.user.role === 'admin' ||
          (member && member.permissions.canEditJobs);

        if (!canUpdate) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update this job',
          });
        }

        const updateData: any = {
          status: input.status,
          updatedAt: new Date(),
        };

        // Set published/closed dates
        if (input.status === 'published' && !job.publishedAt) {
          updateData.publishedAt = new Date();
        } else if (input.status === 'closed' && !job.closedAt) {
          updateData.closedAt = new Date();
        }

        const updatedJob = await Job.findByIdAndUpdate(job._id, updateData, {
          new: true,
          runValidators: true,
        });

        return updatedJob;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Soft delete job
   */
  delete: recruiterProcedure.input(IdSchema).mutation(async ({ input, ctx }) => {
    try {
      const job = await Job.findOne({
        $or: [{ jobId: input.id }, { _id: input.id }],
        isDeleted: { $ne: true },
      });

      if (!job) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Job not found',
        });
      }

      // Check permissions
      const member = await OrganizationMember.findOne({
        organizationId: job.organizationId,
        userId: ctx.user.id,
        status: 'active',
        isDeleted: { $ne: true },
      });

      const canDelete =
        job.postedBy === ctx.user.id ||
        ctx.user.role === 'admin' ||
        (member && member.permissions.canDeleteJobs);

      if (!canDelete) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to delete this job',
        });
      }

      await softDelete(Job, job._id.toString(), ctx.user.id);

      // Update organization job count
      await Organization.findOneAndUpdate(
        { organizationId: job.organizationId },
        { $inc: { jobCount: -1 } }
      );

      return { success: true, message: 'Job deleted successfully' };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Bulk update job status
   */
  bulkUpdateStatus: recruiterProcedure
    .input(
      z.object({
        jobIds: z.array(z.string()).min(1),
        status: JobStatusSchema,
        organizationId: z.string(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Check permissions for the organization
        const member = await OrganizationMember.findOne({
          organizationId: input.organizationId,
          userId: ctx.user.id,
          status: 'active',
          isDeleted: { $ne: true },
        });

        if (!member && ctx.user.role !== 'admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update jobs in this organization',
          });
        }

        const filter: any = {
          _id: { $in: input.jobIds },
          organizationId: input.organizationId,
          isDeleted: { $ne: true },
        };

        // If not admin, only allow updating own jobs or if has edit permission
        if (ctx.user.role !== 'admin' && !member?.permissions.canEditJobs) {
          filter.postedBy = ctx.user.id;
        }

        const updateData: any = {
          status: input.status,
          updatedAt: new Date(),
        };

        if (input.status === 'published') {
          updateData.publishedAt = new Date();
        } else if (input.status === 'closed') {
          updateData.closedAt = new Date();
        }

        const result = await Job.updateMany(filter, updateData);

        return {
          success: true,
          message: `Updated ${result.modifiedCount} jobs`,
          modifiedCount: result.modifiedCount,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Get job statistics
   */
  getStats: recruiterProcedure
    .input(z.object({ organizationId: z.string().optional() }))
    .query(async ({ input, ctx }) => {
      try {
        const filter: any = { isDeleted: { $ne: true } };

        if (input.organizationId) {
          // Check permissions for specific organization
          const member = await OrganizationMember.findOne({
            organizationId: input.organizationId,
            userId: ctx.user.id,
            status: 'active',
            isDeleted: { $ne: true },
          });

          if (!member && ctx.user.role !== 'admin') {
            throw new TRPCError({
              code: 'FORBIDDEN',
              message: 'You do not have permission to view statistics for this organization',
            });
          }

          filter.organizationId = input.organizationId;
        } else if (ctx.user.role !== 'admin') {
          // For non-admins without specific org, show only their jobs
          filter.postedBy = ctx.user.id;
        }

        const [totalJobs, publishedJobs, draftJobs, closedJobs, totalApplications, totalViews] =
          await Promise.all([
            Job.countDocuments(filter),
            Job.countDocuments({ ...filter, status: 'published' }),
            Job.countDocuments({ ...filter, status: 'draft' }),
            Job.countDocuments({ ...filter, status: 'closed' }),
            Job.aggregate([
              { $match: filter },
              { $group: { _id: null, total: { $sum: '$applicationCount' } } },
            ]).then((result) => result[0]?.total || 0),
            Job.aggregate([
              { $match: filter },
              { $group: { _id: null, total: { $sum: '$viewCount' } } },
            ]).then((result) => result[0]?.total || 0),
          ]);

        return {
          totalJobs,
          publishedJobs,
          draftJobs,
          closedJobs,
          totalApplications,
          totalViews,
          averageApplicationsPerJob: totalJobs > 0 ? totalApplications / totalJobs : 0,
          averageViewsPerJob: totalJobs > 0 ? totalViews / totalJobs : 0,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Search jobs
   */
  search: publicProcedure
    .input(
      z.object({
        query: z.string().min(1),
        filters: z
          .object({
            location: z.string().optional(),
            type: JobTypeSchema.optional(),
            experienceLevel: JobExperienceLevelSchema.optional(),
            workLocation: WorkLocationSchema.optional(),
            salaryMin: z.number().optional(),
            salaryMax: z.number().optional(),
          })
          .optional(),
        limit: z.number().min(1).max(50).default(20),
      })
    )
    .query(async ({ input }) => {
      try {
        const filter: any = {
          isDeleted: { $ne: true },
          status: 'published',
          $or: [
            { title: { $regex: input.query, $options: 'i' } },
            { description: { $regex: input.query, $options: 'i' } },
            { tags: { $in: [new RegExp(input.query, 'i')] } },
          ],
        };

        // Apply additional filters
        if (input.filters) {
          Object.entries(input.filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              if (key === 'location') {
                filter.$and = filter.$and || [];
                filter.$and.push({
                  $or: [
                    { 'location.city': { $regex: value, $options: 'i' } },
                    { 'location.state': { $regex: value, $options: 'i' } },
                    { 'location.country': { $regex: value, $options: 'i' } },
                  ],
                });
              } else if (key === 'salaryMin') {
                filter['salaryRange.min'] = { $gte: value };
              } else if (key === 'salaryMax') {
                filter['salaryRange.max'] = { $lte: value };
              } else {
                filter[key] = value;
              }
            }
          });
        }

        const jobs = await Job.find(filter)
          .limit(input.limit)
          .sort({ createdAt: -1 })
          .select(
            'jobId title organizationId location type experienceLevel workLocation salaryRange createdAt'
          )
          .lean();

        return jobs;
      } catch (error) {
        handleApiError(error);
      }
    }),
});
