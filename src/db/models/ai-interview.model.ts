import mongoose, { Document, Model, Schema } from 'mongoose';
import { nanoid } from 'nanoid';
import { z } from 'zod';

// Zod validation schemas
export const InterviewStatusSchema = z.enum([
  'scheduled',
  'in_progress',
  'completed',
  'cancelled',
  'failed',
  'expired',
]);

export const InterviewTypeSchema = z.enum([
  'technical',
  'behavioral',
  'cultural_fit',
  'leadership',
  'problem_solving',
  'communication',
  'custom',
]);

export const QuestionTypeSchema = z.enum([
  'multiple_choice',
  'open_ended',
  'coding',
  'scenario_based',
  'video_response',
  'audio_response',
]);

export const QuestionSchema = z.object({
  questionId: z.string().default(() => `q_${nanoid(8)}`),
  type: QuestionTypeSchema,
  category: z.string(),
  question: z.string().min(1, 'Question text is required'),
  options: z.array(z.string()).optional(), // For multiple choice
  expectedAnswer: z.string().optional(),
  timeLimit: z.number().min(30).max(3600).optional(), // seconds
  difficulty: z.enum(['easy', 'medium', 'hard']).default('medium'),
  skills: z.array(z.string()).default([]),
  weight: z.number().min(0).max(1).default(1), // Question weight in scoring
});

export const ResponseSchema = z.object({
  questionId: z.string(),
  answer: z.string(),
  timeSpent: z.number().optional(), // seconds
  submittedAt: z.date().default(() => new Date()),
  isCorrect: z.boolean().optional(),
  score: z.number().min(0).max(100).optional(),
  confidence: z.number().min(0).max(1).optional(),
  keyPoints: z.array(z.string()).default([]),
  missingPoints: z.array(z.string()).default([]),
});

export const AnalysisMetricsSchema = z.object({
  overallScore: z.number().min(0).max(100),
  technicalScore: z.number().min(0).max(100).optional(),
  communicationScore: z.number().min(0).max(100).optional(),
  problemSolvingScore: z.number().min(0).max(100).optional(),
  culturalFitScore: z.number().min(0).max(100).optional(),
  confidenceLevel: z.number().min(0).max(1),
  responseTime: z.number().min(0), // average response time in seconds
  completionRate: z.number().min(0).max(1),
  consistencyScore: z.number().min(0).max(1).optional(),
});

export const SkillAssessmentSchema = z.object({
  skill: z.string(),
  level: z.enum(['beginner', 'intermediate', 'advanced', 'expert']),
  score: z.number().min(0).max(100),
  confidence: z.number().min(0).max(1),
  evidence: z.array(z.string()).default([]),
  recommendations: z.array(z.string()).default([]),
});

export const PersonalityTraitsSchema = z.object({
  openness: z.number().min(0).max(1).optional(),
  conscientiousness: z.number().min(0).max(1).optional(),
  extraversion: z.number().min(0).max(1).optional(),
  agreeableness: z.number().min(0).max(1).optional(),
  neuroticism: z.number().min(0).max(1).optional(),
  leadership: z.number().min(0).max(1).optional(),
  teamwork: z.number().min(0).max(1).optional(),
  adaptability: z.number().min(0).max(1).optional(),
  creativity: z.number().min(0).max(1).optional(),
  analyticalThinking: z.number().min(0).max(1).optional(),
});

export const RecommendationSchema = z.object({
  type: z.enum(['hire', 'reject', 'further_review', 'conditional_hire']),
  confidence: z.number().min(0).max(1),
  reasoning: z.string(),
  strengths: z.array(z.string()).default([]),
  weaknesses: z.array(z.string()).default([]),
  improvementAreas: z.array(z.string()).default([]),
  nextSteps: z.array(z.string()).default([]),
});

export const CreateAiInterviewSchema = z.object({
  applicationId: z.string().min(1, 'Application ID is required'),
  jobId: z.string().min(1, 'Job ID is required'),
  candidateId: z.string().min(1, 'Candidate ID is required'),
  interviewType: InterviewTypeSchema,
  questions: z.array(QuestionSchema).min(1, 'At least one question is required'),
  timeLimit: z.number().min(300).max(7200).default(3600), // seconds (5 min to 2 hours)
  scheduledAt: z.date().optional(),
  expiresAt: z.date().optional(),
  instructions: z.string().optional(),
  allowRetake: z.boolean().default(false),
  maxAttempts: z.number().min(1).max(5).default(1),
});

export const UpdateAiInterviewSchema = z.object({
  status: InterviewStatusSchema.optional(),
  responses: z.array(ResponseSchema).optional(),
  analysisMetrics: AnalysisMetricsSchema.optional(),
  skillAssessments: z.array(SkillAssessmentSchema).optional(),
  personalityTraits: PersonalityTraitsSchema.optional(),
  recommendation: RecommendationSchema.optional(),
  feedback: z.string().optional(),
  notes: z.string().optional(),
});

// TypeScript interfaces
export type InterviewStatus = z.infer<typeof InterviewStatusSchema>;
export type InterviewType = z.infer<typeof InterviewTypeSchema>;
export type QuestionType = z.infer<typeof QuestionTypeSchema>;
export type Question = z.infer<typeof QuestionSchema>;
export type Response = z.infer<typeof ResponseSchema>;
export type AnalysisMetrics = z.infer<typeof AnalysisMetricsSchema>;
export type SkillAssessment = z.infer<typeof SkillAssessmentSchema>;
export type PersonalityTraits = z.infer<typeof PersonalityTraitsSchema>;
export type Recommendation = z.infer<typeof RecommendationSchema>;
export type CreateAiInterviewInput = z.infer<typeof CreateAiInterviewSchema>;
export type UpdateAiInterviewInput = z.infer<typeof UpdateAiInterviewSchema>;

export interface IAiInterview extends Document {
  _id: mongoose.Types.ObjectId;
  interviewId: string;
  applicationId: string;
  jobId: string;
  candidateId: string;
  interviewType: InterviewType;
  status: InterviewStatus;
  questions: Question[];
  responses: Response[];
  timeLimit: number;
  scheduledAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  expiresAt?: Date;
  instructions?: string;
  allowRetake: boolean;
  maxAttempts: number;
  currentAttempt: number;
  analysisMetrics?: AnalysisMetrics;
  skillAssessments: SkillAssessment[];
  personalityTraits?: PersonalityTraits;
  recommendation?: Recommendation;
  feedback?: string;
  notes?: string;
  aiModelVersion: string;
  processingTime?: number; // milliseconds
  isAnalyzed: boolean;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  start(): Promise<void>;
  complete(): Promise<void>;
  cancel(reason?: string): Promise<void>;
  addResponse(response: Response): Promise<void>;
  calculateScore(): number;
  isExpired(): boolean;
  canRetake(): boolean;
  generateAnalysis(): Promise<void>;
  getNextQuestion(): Question | null;
  getCurrentProgress(): { completed: number; total: number; percentage: number };
  getSummary(): Partial<IAiInterview>;
}

// Static methods interface
export interface IAiInterviewModel extends Model<IAiInterview> {
  createInterview(interviewData: CreateAiInterviewInput): Promise<IAiInterview>;
  findByInterviewId(interviewId: string): Promise<IAiInterview | null>;
  findByApplication(applicationId: string): Promise<IAiInterview[]>;
  findByCandidate(candidateId: string): Promise<IAiInterview[]>;
  findByJob(jobId: string): Promise<IAiInterview[]>;
  findPendingAnalysis(): Promise<IAiInterview[]>;
  getInterviewStats(jobId?: string, organizationId?: string): Promise<any>;
  findExpiredInterviews(): Promise<IAiInterview[]>;
}

// Mongoose schema
const aiInterviewSchema = new Schema<IAiInterview>(
  {
    interviewId: {
      type: String,
      unique: true,
      default: () => `ai_${nanoid(12)}`,
      index: true,
    },
    applicationId: {
      type: String,
      required: true,
      index: true,
    },
    jobId: {
      type: String,
      required: true,
      index: true,
    },
    candidateId: {
      type: String,
      required: true,
      index: true,
    },
    interviewType: {
      type: String,
      enum: [
        'technical',
        'behavioral',
        'cultural_fit',
        'leadership',
        'problem_solving',
        'communication',
        'custom',
      ],
      required: true,
      index: true,
    },
    status: {
      type: String,
      enum: ['scheduled', 'in_progress', 'completed', 'cancelled', 'failed', 'expired'],
      default: 'scheduled',
      index: true,
    },
    questions: [
      {
        questionId: { type: String, required: true },
        type: {
          type: String,
          enum: [
            'multiple_choice',
            'open_ended',
            'coding',
            'scenario_based',
            'video_response',
            'audio_response',
          ],
          required: true,
        },
        category: { type: String, required: true },
        question: { type: String, required: true },
        options: [String],
        expectedAnswer: String,
        timeLimit: Number,
        difficulty: {
          type: String,
          enum: ['easy', 'medium', 'hard'],
          default: 'medium',
        },
        skills: [String],
        weight: { type: Number, default: 1 },
      },
    ],
    responses: [
      {
        questionId: { type: String, required: true },
        answer: { type: String, required: true },
        timeSpent: Number,
        submittedAt: { type: Date, default: Date.now },
        isCorrect: Boolean,
        score: Number,
        confidence: Number,
        keyPoints: [String],
        missingPoints: [String],
      },
    ],
    timeLimit: {
      type: Number,
      default: 3600,
    },
    scheduledAt: {
      type: Date,
      index: true,
    },
    startedAt: {
      type: Date,
      index: true,
    },
    completedAt: {
      type: Date,
      index: true,
    },
    expiresAt: {
      type: Date,
      index: true,
    },
    instructions: {
      type: String,
    },
    allowRetake: {
      type: Boolean,
      default: false,
    },
    maxAttempts: {
      type: Number,
      default: 1,
    },
    currentAttempt: {
      type: Number,
      default: 1,
    },
    analysisMetrics: {
      overallScore: Number,
      technicalScore: Number,
      communicationScore: Number,
      problemSolvingScore: Number,
      culturalFitScore: Number,
      confidenceLevel: Number,
      responseTime: Number,
      completionRate: Number,
      consistencyScore: Number,
    },
    skillAssessments: [
      {
        skill: { type: String, required: true },
        level: {
          type: String,
          enum: ['beginner', 'intermediate', 'advanced', 'expert'],
          required: true,
        },
        score: { type: Number, required: true },
        confidence: { type: Number, required: true },
        evidence: [String],
        recommendations: [String],
      },
    ],
    personalityTraits: {
      openness: Number,
      conscientiousness: Number,
      extraversion: Number,
      agreeableness: Number,
      neuroticism: Number,
      leadership: Number,
      teamwork: Number,
      adaptability: Number,
      creativity: Number,
      analyticalThinking: Number,
    },
    recommendation: {
      type: {
        type: String,
        enum: ['hire', 'reject', 'further_review', 'conditional_hire'],
      },
      confidence: Number,
      reasoning: String,
      strengths: [String],
      weaknesses: [String],
      improvementAreas: [String],
      nextSteps: [String],
    },
    feedback: {
      type: String,
    },
    notes: {
      type: String,
    },
    aiModelVersion: {
      type: String,
      default: '1.0',
    },
    processingTime: {
      type: Number,
    },
    isAnalyzed: {
      type: Boolean,
      default: false,
      index: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes
aiInterviewSchema.index({ candidateId: 1, status: 1 });
aiInterviewSchema.index({ jobId: 1, status: 1 });
aiInterviewSchema.index({ status: 1, scheduledAt: 1 });
aiInterviewSchema.index({ isAnalyzed: 1, completedAt: 1 });
aiInterviewSchema.index({ createdAt: -1 });

// Instance methods
aiInterviewSchema.methods.start = async function (): Promise<void> {
  this.status = 'in_progress';
  this.startedAt = new Date();

  // Set expiration if not already set
  if (!this.expiresAt) {
    this.expiresAt = new Date(Date.now() + this.timeLimit * 1000);
  }

  await this.save();
};

aiInterviewSchema.methods.complete = async function (): Promise<void> {
  this.status = 'completed';
  this.completedAt = new Date();
  await this.save();
};

aiInterviewSchema.methods.cancel = async function (reason?: string): Promise<void> {
  this.status = 'cancelled';
  if (reason) {
    this.notes = reason;
  }
  await this.save();
};

aiInterviewSchema.methods.addResponse = async function (response: Response): Promise<void> {
  // Validate response
  const validatedResponse = ResponseSchema.parse(response);

  // Check if response for this question already exists
  const existingResponseIndex = this.responses.findIndex(
    (r: any) => r.questionId === validatedResponse.questionId
  );

  if (existingResponseIndex >= 0) {
    // Update existing response
    this.responses[existingResponseIndex] = validatedResponse;
  } else {
    // Add new response
    this.responses.push(validatedResponse);
  }

  // Check if all questions are answered
  if (this.responses.length >= this.questions.length) {
    await this.complete();
  }

  await this.save();
};

aiInterviewSchema.methods.calculateScore = function (): number {
  if (this.responses.length === 0) return 0;

  let totalScore = 0;
  let totalWeight = 0;

  this.responses.forEach((response: any) => {
    const question = this.questions.find((q: any) => q.questionId === response.questionId);
    if (question && response.score !== undefined) {
      totalScore += response.score * question.weight;
      totalWeight += question.weight;
    }
  });

  return totalWeight > 0 ? totalScore / totalWeight : 0;
};

aiInterviewSchema.methods.isExpired = function (): boolean {
  return !!(this.expiresAt && this.expiresAt < new Date());
};

aiInterviewSchema.methods.canRetake = function (): boolean {
  return this.allowRetake && this.currentAttempt < this.maxAttempts;
};

aiInterviewSchema.methods.generateAnalysis = async function (): Promise<void> {
  // This would typically call an AI service to analyze responses
  // For now, we'll create a basic analysis based on responses

  const overallScore = this.calculateScore();
  const completionRate = this.responses.length / this.questions.length;

  // Calculate average response time
  const responseTimes = this.responses.filter((r: any) => r.timeSpent).map((r: any) => r.timeSpent);
  const avgResponseTime =
    responseTimes.length > 0
      ? responseTimes.reduce((a: any, b: any) => a + b, 0) / responseTimes.length
      : 0;

  // Calculate confidence level
  const confidenceScores = this.responses
    .filter((r: any) => r.confidence !== undefined)
    .map((r: any) => r.confidence);
  const avgConfidence =
    confidenceScores.length > 0
      ? confidenceScores.reduce((a: any, b: any) => a + b, 0) / confidenceScores.length
      : 0.5;

  this.analysisMetrics = {
    overallScore,
    confidenceLevel: avgConfidence,
    responseTime: avgResponseTime,
    completionRate,
  };

  // Generate basic recommendation
  let recommendationType: 'hire' | 'reject' | 'further_review' | 'conditional_hire';
  let confidence: number;

  if (overallScore >= 80 && completionRate >= 0.9) {
    recommendationType = 'hire';
    confidence = 0.8;
  } else if (overallScore >= 60 && completionRate >= 0.7) {
    recommendationType = 'further_review';
    confidence = 0.6;
  } else if (overallScore >= 40) {
    recommendationType = 'conditional_hire';
    confidence = 0.4;
  } else {
    recommendationType = 'reject';
    confidence = 0.7;
  }

  this.recommendation = {
    type: recommendationType,
    confidence,
    reasoning: `Based on overall score of ${overallScore.toFixed(1)}% and completion rate of ${(completionRate * 100).toFixed(1)}%`,
    strengths: [],
    weaknesses: [],
    improvementAreas: [],
    nextSteps: [],
  };

  this.isAnalyzed = true;
  await this.save();
};

aiInterviewSchema.methods.getNextQuestion = function (): Question | null {
  const answeredQuestionIds = this.responses.map((r: any) => r.questionId);
  const nextQuestion = this.questions.find((q: any) => !answeredQuestionIds.includes(q.questionId));
  return nextQuestion || null;
};

aiInterviewSchema.methods.getCurrentProgress = function (): {
  completed: number;
  total: number;
  percentage: number;
} {
  const completed = this.responses.length;
  const total = this.questions.length;
  const percentage = total > 0 ? (completed / total) * 100 : 0;

  return { completed, total, percentage };
};

aiInterviewSchema.methods.getSummary = function (): Partial<IAiInterview> {
  return {
    interviewId: this.interviewId,
    applicationId: this.applicationId,
    jobId: this.jobId,
    candidateId: this.candidateId,
    interviewType: this.interviewType,
    status: this.status,
    scheduledAt: this.scheduledAt,
    startedAt: this.startedAt,
    completedAt: this.completedAt,
    analysisMetrics: this.analysisMetrics,
    recommendation: this.recommendation,
    isAnalyzed: this.isAnalyzed,
    createdAt: this.createdAt,
  };
};

// Static methods
aiInterviewSchema.statics.createInterview = async function (
  interviewData: CreateAiInterviewInput
): Promise<IAiInterview> {
  // Validate input with Zod
  const validatedData = CreateAiInterviewSchema.parse(interviewData);

  // Check if interview already exists for this application
  const existingInterview = await this.findOne({
    applicationId: validatedData.applicationId,
    status: { $nin: ['completed', 'cancelled', 'expired'] },
  });

  if (existingInterview) {
    throw new Error('Active interview already exists for this application');
  }

  // Set default expiration if not provided
  if (!validatedData.expiresAt) {
    validatedData.expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
  }

  // Create new interview
  const interview = new this(validatedData);
  await interview.save();

  return interview;
};

aiInterviewSchema.statics.findByInterviewId = async function (
  interviewId: string
): Promise<IAiInterview | null> {
  return this.findOne({ interviewId });
};

aiInterviewSchema.statics.findByApplication = async function (
  applicationId: string
): Promise<IAiInterview[]> {
  return this.find({ applicationId }).sort({ createdAt: -1 });
};

aiInterviewSchema.statics.findByCandidate = async function (
  candidateId: string
): Promise<IAiInterview[]> {
  return this.find({ candidateId }).sort({ createdAt: -1 });
};

aiInterviewSchema.statics.findByJob = async function (jobId: string): Promise<IAiInterview[]> {
  return this.find({ jobId }).sort({ createdAt: -1 });
};

aiInterviewSchema.statics.findPendingAnalysis = async function (): Promise<IAiInterview[]> {
  return this.find({
    status: 'completed',
    isAnalyzed: false,
  }).sort({ completedAt: 1 });
};

aiInterviewSchema.statics.getInterviewStats = async function (
  jobId?: string,
  organizationId?: string
): Promise<any> {
  const matchStage: any = {};

  if (jobId) {
    matchStage.jobId = jobId;
  } else if (organizationId) {
    // Need to join with jobs to filter by organization
    const pipeline = [
      {
        $lookup: {
          from: 'jobs',
          localField: 'jobId',
          foreignField: 'jobId',
          as: 'job',
        },
      },
      {
        $match: {
          'job.organizationId': organizationId,
        },
      },
      {
        $group: {
          _id: null,
          totalInterviews: { $sum: 1 },
          completedInterviews: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] },
          },
          avgScore: { $avg: '$analysisMetrics.overallScore' },
          avgCompletionRate: { $avg: '$analysisMetrics.completionRate' },
          byType: {
            $push: {
              type: '$interviewType',
              count: 1,
            },
          },
          byRecommendation: {
            $push: {
              recommendation: '$recommendation.type',
              count: 1,
            },
          },
        },
      },
    ];

    const result = await this.aggregate(pipeline);
    return (
      result[0] || {
        totalInterviews: 0,
        completedInterviews: 0,
        avgScore: 0,
        avgCompletionRate: 0,
        byType: [],
        byRecommendation: [],
      }
    );
  }

  const pipeline = [
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalInterviews: { $sum: 1 },
        scheduledInterviews: {
          $sum: { $cond: [{ $eq: ['$status', 'scheduled'] }, 1, 0] },
        },
        inProgressInterviews: {
          $sum: { $cond: [{ $eq: ['$status', 'in_progress'] }, 1, 0] },
        },
        completedInterviews: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] },
        },
        cancelledInterviews: {
          $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] },
        },
        expiredInterviews: {
          $sum: { $cond: [{ $eq: ['$status', 'expired'] }, 1, 0] },
        },
        avgScore: { $avg: '$analysisMetrics.overallScore' },
        avgCompletionRate: { $avg: '$analysisMetrics.completionRate' },
        avgResponseTime: { $avg: '$analysisMetrics.responseTime' },
      },
    },
  ];

  const result = await this.aggregate(pipeline);
  return (
    result[0] || {
      totalInterviews: 0,
      scheduledInterviews: 0,
      inProgressInterviews: 0,
      completedInterviews: 0,
      cancelledInterviews: 0,
      expiredInterviews: 0,
      avgScore: 0,
      avgCompletionRate: 0,
      avgResponseTime: 0,
    }
  );
};

aiInterviewSchema.statics.findExpiredInterviews = async function (): Promise<IAiInterview[]> {
  return this.find({
    expiresAt: { $lt: new Date() },
    status: { $nin: ['completed', 'cancelled', 'expired'] },
  });
};

// Create and export the model
export const AiInterview =
  (mongoose.models.AiInterview as IAiInterviewModel) ||
  mongoose.model<IAiInterview, IAiInterviewModel>('AiInterview', aiInterviewSchema);

// Helper functions for Next.js API routes
export class AiInterviewService {
  static async createInterview(interviewData: CreateAiInterviewInput): Promise<IAiInterview> {
    try {
      return await AiInterview.createInterview(interviewData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  static async getInterviewById(interviewId: string): Promise<IAiInterview | null> {
    return await AiInterview.findByInterviewId(interviewId);
  }

  static async updateInterview(
    interviewId: string,
    updateData: UpdateAiInterviewInput
  ): Promise<IAiInterview | null> {
    const validatedData = UpdateAiInterviewSchema.parse(updateData);
    return await AiInterview.findOneAndUpdate(
      { interviewId },
      { $set: validatedData },
      { new: true, runValidators: true }
    );
  }

  static async getApplicationInterviews(applicationId: string): Promise<IAiInterview[]> {
    return await AiInterview.findByApplication(applicationId);
  }

  static async getCandidateInterviews(candidateId: string): Promise<IAiInterview[]> {
    return await AiInterview.findByCandidate(candidateId);
  }

  static async getJobInterviews(jobId: string): Promise<IAiInterview[]> {
    return await AiInterview.findByJob(jobId);
  }

  static async startInterview(interviewId: string): Promise<IAiInterview | null> {
    const interview = await AiInterview.findByInterviewId(interviewId);
    if (interview && interview.status === 'scheduled') {
      await interview.start();
      return interview;
    }
    return null;
  }

  static async completeInterview(interviewId: string): Promise<IAiInterview | null> {
    const interview = await AiInterview.findByInterviewId(interviewId);
    if (interview && interview.status === 'in_progress') {
      await interview.complete();
      return interview;
    }
    return null;
  }

  static async cancelInterview(interviewId: string, reason?: string): Promise<IAiInterview | null> {
    const interview = await AiInterview.findByInterviewId(interviewId);
    if (interview && interview.status !== 'completed') {
      await interview.cancel(reason);
      return interview;
    }
    return null;
  }

  static async submitResponse(
    interviewId: string,
    response: Response
  ): Promise<IAiInterview | null> {
    const interview = await AiInterview.findByInterviewId(interviewId);
    if (interview && interview.status === 'in_progress') {
      await interview.addResponse(response);
      return interview;
    }
    return null;
  }

  static async getNextQuestion(interviewId: string): Promise<Question | null> {
    const interview = await AiInterview.findByInterviewId(interviewId);
    return interview ? interview.getNextQuestion() : null;
  }

  static async getInterviewProgress(
    interviewId: string
  ): Promise<{ completed: number; total: number; percentage: number } | null> {
    const interview = await AiInterview.findByInterviewId(interviewId);
    return interview ? interview.getCurrentProgress() : null;
  }

  static async generateAnalysis(interviewId: string): Promise<IAiInterview | null> {
    const interview = await AiInterview.findByInterviewId(interviewId);
    if (interview && interview.status === 'completed' && !interview.isAnalyzed) {
      await interview.generateAnalysis();
      return interview;
    }
    return null;
  }

  static async getInterviewStats(jobId?: string, organizationId?: string): Promise<any> {
    return await AiInterview.getInterviewStats(jobId, organizationId);
  }

  static async getPendingAnalysis(): Promise<IAiInterview[]> {
    return await AiInterview.findPendingAnalysis();
  }

  static async getExpiredInterviews(): Promise<IAiInterview[]> {
    return await AiInterview.findExpiredInterviews();
  }

  static async markExpiredInterviews(): Promise<number> {
    const expiredInterviews = await this.getExpiredInterviews();
    let count = 0;

    for (const interview of expiredInterviews) {
      interview.status = 'expired';
      await interview.save();
      count++;
    }

    return count;
  }

  static async getInterviewSummary(interviewId: string): Promise<Partial<IAiInterview> | null> {
    const interview = await AiInterview.findByInterviewId(interviewId);
    return interview ? interview.getSummary() : null;
  }

  static async deleteInterview(interviewId: string): Promise<boolean> {
    const result = await AiInterview.deleteOne({ interviewId });
    return result.deletedCount === 1;
  }

  // Advanced filtering and analytics
  static async getInterviewsWithFilters(filters: {
    candidateId?: string;
    jobId?: string;
    applicationId?: string;
    status?: InterviewStatus[];
    interviewType?: InterviewType[];
    organizationId?: string;
    scheduledAfter?: Date;
    scheduledBefore?: Date;
    completedAfter?: Date;
    completedBefore?: Date;
    minScore?: number;
    maxScore?: number;
    limit?: number;
    offset?: number;
  }): Promise<IAiInterview[]> {
    const query: any = {};

    if (filters.candidateId) query.candidateId = filters.candidateId;
    if (filters.jobId) query.jobId = filters.jobId;
    if (filters.applicationId) query.applicationId = filters.applicationId;
    if (filters.status?.length) query.status = { $in: filters.status };
    if (filters.interviewType?.length) query.interviewType = { $in: filters.interviewType };

    if (filters.scheduledAfter || filters.scheduledBefore) {
      query.scheduledAt = {};
      if (filters.scheduledAfter) query.scheduledAt.$gte = filters.scheduledAfter;
      if (filters.scheduledBefore) query.scheduledAt.$lte = filters.scheduledBefore;
    }

    if (filters.completedAfter || filters.completedBefore) {
      query.completedAt = {};
      if (filters.completedAfter) query.completedAt.$gte = filters.completedAfter;
      if (filters.completedBefore) query.completedAt.$lte = filters.completedBefore;
    }

    if (filters.minScore || filters.maxScore) {
      query['analysisMetrics.overallScore'] = {};
      if (filters.minScore) query['analysisMetrics.overallScore'].$gte = filters.minScore;
      if (filters.maxScore) query['analysisMetrics.overallScore'].$lte = filters.maxScore;
    }

    if (filters.organizationId) {
      // Use aggregation to filter by organization
      return AiInterview.aggregate([
        {
          $lookup: {
            from: 'jobs',
            localField: 'jobId',
            foreignField: 'jobId',
            as: 'job',
          },
        },
        {
          $match: {
            'job.organizationId': filters.organizationId,
            ...query,
          },
        },
        {
          $sort: { createdAt: -1 },
        },
        ...(filters.offset ? [{ $skip: filters.offset }] : []),
        ...(filters.limit ? [{ $limit: filters.limit }] : []),
      ]);
    }

    return AiInterview.find(query)
      .sort({ createdAt: -1 })
      .limit(filters.limit || 50)
      .skip(filters.offset || 0);
  }

  // Bulk operations
  static async bulkAnalyzeInterviews(interviewIds: string[]): Promise<number> {
    let count = 0;

    for (const interviewId of interviewIds) {
      const interview = await this.generateAnalysis(interviewId);
      if (interview) count++;
    }

    return count;
  }

  static async getAverageScoreBySkill(skill: string, organizationId?: string): Promise<number> {
    const matchStage: any = {
      'skillAssessments.skill': skill,
      isAnalyzed: true,
    };

    let pipeline: any[] = [
      { $match: matchStage },
      { $unwind: '$skillAssessments' },
      { $match: { 'skillAssessments.skill': skill } },
      {
        $group: {
          _id: null,
          avgScore: { $avg: '$skillAssessments.score' },
        },
      },
    ];

    if (organizationId) {
      pipeline = [
        {
          $lookup: {
            from: 'jobs',
            localField: 'jobId',
            foreignField: 'jobId',
            as: 'job',
          },
        },
        {
          $match: {
            'job.organizationId': organizationId,
            ...matchStage,
          },
        },
        ...pipeline.slice(1),
      ];
    }

    const result = await AiInterview.aggregate(pipeline);
    return result[0]?.avgScore || 0;
  }
}
