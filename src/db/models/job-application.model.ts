import mongoose, { Document, Model, Schema } from 'mongoose';
import { nanoid } from 'nanoid';
import { z } from 'zod';

// Zod validation schemas
export const ApplicationStatusSchema = z.enum([
  'submitted',
  'under_review',
  'shortlisted',
  'interview_scheduled',
  'interview_completed',
  'offer_extended',
  'offer_accepted',
  'offer_declined',
  'rejected',
  'withdrawn',
  'on_hold',
]);

export const ApplicationSourceSchema = z.enum([
  'direct',
  'job_board',
  'referral',
  'social_media',
  'company_website',
  'recruiter',
  'other',
]);

export const InterviewStageSchema = z.object({
  stageId: z.string(),
  name: z.string(),
  type: z.enum(['phone', 'video', 'onsite', 'technical', 'behavioral', 'ai_interview']),
  status: z.enum(['pending', 'scheduled', 'completed', 'cancelled', 'no_show']).default('pending'),
  scheduledAt: z.date().optional(),
  completedAt: z.date().optional(),
  duration: z.number().optional(), // minutes
  interviewerIds: z.array(z.string()).default([]),
  feedback: z.string().optional(),
  score: z.number().min(0).max(10).optional(),
  notes: z.string().optional(),
  recordingUrl: z.string().url().optional(),
  aiAnalysisId: z.string().optional(),
});

export const DocumentSchema = z.object({
  name: z.string().min(1, 'Document name is required'),
  type: z.enum(['resume', 'cover_letter', 'portfolio', 'certificate', 'other']),
  url: z.string().url('Invalid document URL'),
  uploadedAt: z.date().default(() => new Date()),
  size: z.number().optional(), // bytes
  mimeType: z.string().optional(),
});

export const CreateJobApplicationSchema = z.object({
  jobId: z.string().min(1, 'Job ID is required'),
  applicantId: z.string().min(1, 'Applicant ID is required'),
  coverLetter: z.string().optional(),
  expectedSalary: z
    .object({
      amount: z.number().min(0).optional(),
      currency: z.string().length(3).default('USD'),
    })
    .optional(),
  availabilityDate: z.date().optional(),
  source: ApplicationSourceSchema.default('direct'),
  referredBy: z.string().optional(),
  documents: z.array(DocumentSchema).default([]),
  customFields: z.record(z.unknown()).optional(),
  agreedToTerms: z.boolean().default(true),
  consentToDataProcessing: z.boolean().default(true),
});

export const UpdateJobApplicationSchema = z.object({
  status: ApplicationStatusSchema.optional(),
  coverLetter: z.string().optional(),
  expectedSalary: z
    .object({
      amount: z.number().min(0).optional(),
      currency: z.string().length(3).default('USD'),
    })
    .optional(),
  availabilityDate: z.date().optional(),
  documents: z.array(DocumentSchema).optional(),
  customFields: z.record(z.unknown()).optional(),
  reviewNotes: z.string().optional(),
  rejectionReason: z.string().optional(),
  interviewStages: z.array(InterviewStageSchema).optional(),
});

// TypeScript interfaces
export type ApplicationStatus = z.infer<typeof ApplicationStatusSchema>;
export type ApplicationSource = z.infer<typeof ApplicationSourceSchema>;
export type InterviewStage = z.infer<typeof InterviewStageSchema>;
export type JobDocument = z.infer<typeof DocumentSchema>;
export type CreateJobApplicationInput = z.infer<typeof CreateJobApplicationSchema>;
export type UpdateJobApplicationInput = z.infer<typeof UpdateJobApplicationSchema>;

export interface IJobApplication extends Document {
  _id: mongoose.Types.ObjectId;
  applicationId: string;
  jobId: string;
  applicantId: string;
  status: ApplicationStatus;
  coverLetter?: string;
  expectedSalary?: {
    amount?: number;
    currency: string;
  };
  availabilityDate?: Date;
  source: ApplicationSource;
  referredBy?: string;
  documents: Document[];
  customFields?: Record<string, unknown>;
  agreedToTerms: boolean;
  consentToDataProcessing: boolean;
  reviewNotes?: string;
  rejectionReason?: string;
  interviewStages: InterviewStage[];
  currentStageIndex: number;
  submittedAt: Date;
  lastUpdatedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  offerExtendedAt?: Date;
  offerAcceptedAt?: Date;
  withdrawnAt?: Date;
  rejectedAt?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  updateStatus(newStatus: ApplicationStatus, updatedBy?: string, notes?: string): Promise<void>;
  addDocument(document: JobDocument): Promise<void>;
  removeDocument(documentName: string): Promise<void>;
  scheduleInterview(stageId: string, scheduledAt: Date, interviewerIds: string[]): Promise<void>;
  completeInterviewStage(stageId: string, feedback?: string, score?: number): Promise<void>;
  moveToNextStage(): Promise<void>;
  withdraw(reason?: string): Promise<void>;
  reject(reason: string, rejectedBy?: string): Promise<void>;
  extendOffer(): Promise<void>;
  acceptOffer(): Promise<void>;
  declineOffer(): Promise<void>;
  getCurrentStage(): InterviewStage | null;
  getStatusHistory(): any[];
  canWithdraw(): boolean;
  canReject(): boolean;
  isActive(): boolean;
}

// Static methods interface
export interface IJobApplicationModel extends Model<IJobApplication> {
  createApplication(applicationData: CreateJobApplicationInput): Promise<IJobApplication>;
  findByApplicationId(applicationId: string): Promise<IJobApplication | null>;
  findByJob(jobId: string): Promise<IJobApplication[]>;
  findByApplicant(applicantId: string): Promise<IJobApplication[]>;
  findByStatus(status: ApplicationStatus): Promise<IJobApplication[]>;
  findByOrganization(organizationId: string): Promise<IJobApplication[]>;
  getApplicationStats(jobId?: string, organizationId?: string): Promise<any>;
  findPendingApplications(organizationId?: string): Promise<IJobApplication[]>;
  findApplicationsForReview(organizationId?: string): Promise<IJobApplication[]>;
}

// Mongoose schema
const jobApplicationSchema = new Schema<IJobApplication>(
  {
    applicationId: {
      type: String,
      unique: true,
      default: () => `app_${nanoid(12)}`,
      index: true,
    },
    jobId: {
      type: String,
      required: true,
      index: true,
    },
    applicantId: {
      type: String,
      required: true,
      index: true,
    },
    status: {
      type: String,
      enum: [
        'submitted',
        'under_review',
        'shortlisted',
        'interview_scheduled',
        'interview_completed',
        'offer_extended',
        'offer_accepted',
        'offer_declined',
        'rejected',
        'withdrawn',
        'on_hold',
      ],
      default: 'submitted',
      index: true,
    },
    coverLetter: {
      type: String,
    },
    expectedSalary: {
      amount: Number,
      currency: {
        type: String,
        default: 'USD',
      },
    },
    availabilityDate: {
      type: Date,
    },
    source: {
      type: String,
      enum: [
        'direct',
        'job_board',
        'referral',
        'social_media',
        'company_website',
        'recruiter',
        'other',
      ],
      default: 'direct',
      index: true,
    },
    referredBy: {
      type: String,
      index: true,
    },
    documents: [
      {
        name: { type: String, required: true },
        type: {
          type: String,
          enum: ['resume', 'cover_letter', 'portfolio', 'certificate', 'other'],
          required: true,
        },
        url: { type: String, required: true },
        uploadedAt: { type: Date, default: Date.now },
        size: Number,
        mimeType: String,
      },
    ],
    customFields: {
      type: Schema.Types.Mixed,
    },
    agreedToTerms: {
      type: Boolean,
      default: true,
      required: true,
    },
    consentToDataProcessing: {
      type: Boolean,
      default: true,
      required: true,
    },
    reviewNotes: {
      type: String,
    },
    rejectionReason: {
      type: String,
    },
    interviewStages: [
      {
        stageId: { type: String, required: true },
        name: { type: String, required: true },
        type: {
          type: String,
          enum: ['phone', 'video', 'onsite', 'technical', 'behavioral', 'ai_interview'],
          required: true,
        },
        status: {
          type: String,
          enum: ['pending', 'scheduled', 'completed', 'cancelled', 'no_show'],
          default: 'pending',
        },
        scheduledAt: Date,
        completedAt: Date,
        duration: Number,
        interviewerIds: [String],
        feedback: String,
        score: {
          type: Number,
          min: 0,
          max: 10,
        },
        notes: String,
        recordingUrl: String,
        aiAnalysisId: String,
      },
    ],
    currentStageIndex: {
      type: Number,
      default: 0,
    },
    submittedAt: {
      type: Date,
      default: Date.now,
      index: true,
    },
    lastUpdatedAt: {
      type: Date,
      default: Date.now,
    },
    reviewedAt: {
      type: Date,
      index: true,
    },
    reviewedBy: {
      type: String,
      index: true,
    },
    offerExtendedAt: {
      type: Date,
      index: true,
    },
    offerAcceptedAt: {
      type: Date,
      index: true,
    },
    withdrawnAt: {
      type: Date,
      index: true,
    },
    rejectedAt: {
      type: Date,
      index: true,
    },
  },
  {
    timestamps: true,
  }
);

// Compound indexes
jobApplicationSchema.index({ jobId: 1, applicantId: 1 }, { unique: true });
jobApplicationSchema.index({ jobId: 1, status: 1 });
jobApplicationSchema.index({ applicantId: 1, status: 1 });
jobApplicationSchema.index({ status: 1, submittedAt: -1 });
jobApplicationSchema.index({ source: 1, submittedAt: -1 });
jobApplicationSchema.index({ submittedAt: -1 });

// Pre-save middleware
jobApplicationSchema.pre('save', function (next) {
  this.lastUpdatedAt = new Date();
  next();
});

// Instance methods
jobApplicationSchema.methods.updateStatus = async function (
  newStatus: ApplicationStatus,
  updatedBy?: string,
  notes?: string
): Promise<void> {
  this.status = newStatus;
  this.lastUpdatedAt = new Date();

  if (updatedBy) {
    this.reviewedBy = updatedBy;
    this.reviewedAt = new Date();
  }

  if (notes) {
    this.reviewNotes = notes;
  }

  // Set specific timestamps based on status
  switch (newStatus) {
    case 'offer_extended':
      this.offerExtendedAt = new Date();
      break;
    case 'offer_accepted':
      this.offerAcceptedAt = new Date();
      break;
    case 'rejected':
      this.rejectedAt = new Date();
      break;
    case 'withdrawn':
      this.withdrawnAt = new Date();
      break;
  }

  await this.save();
};

jobApplicationSchema.methods.addDocument = async function (document: JobDocument): Promise<void> {
  // Check if document with same name already exists
  const existingDoc = this.documents.find((doc: any) => doc.name === document.name);
  if (existingDoc) {
    // Replace existing document
    const index = this.documents.indexOf(existingDoc);
    this.documents[index] = document;
  } else {
    this.documents.push(document);
  }
  await this.save();
};

jobApplicationSchema.methods.removeDocument = async function (documentName: string): Promise<void> {
  this.documents = this.documents.filter((doc: any) => doc.name !== documentName);
  await this.save();
};

jobApplicationSchema.methods.scheduleInterview = async function (
  stageId: string,
  scheduledAt: Date,
  interviewerIds: string[]
): Promise<void> {
  const stage = this.interviewStages.find((s: any) => s.stageId === stageId);
  if (stage) {
    stage.status = 'scheduled';
    stage.scheduledAt = scheduledAt;
    stage.interviewerIds = interviewerIds;

    // Update application status if this is the current stage
    if (this.status === 'shortlisted' || this.status === 'under_review') {
      this.status = 'interview_scheduled';
    }

    await this.save();
  }
};

jobApplicationSchema.methods.completeInterviewStage = async function (
  stageId: string,
  feedback?: string,
  score?: number
): Promise<void> {
  const stage = this.interviewStages.find((s: any) => s.stageId === stageId);
  if (stage) {
    stage.status = 'completed';
    stage.completedAt = new Date();
    if (feedback) stage.feedback = feedback;
    if (score !== undefined) stage.score = score;

    // Check if this was the last stage
    const currentStageIndex = this.interviewStages.indexOf(stage);
    if (currentStageIndex === this.interviewStages.length - 1) {
      this.status = 'interview_completed';
    } else {
      // Move to next stage
      this.currentStageIndex = currentStageIndex + 1;
    }

    await this.save();
  }
};

jobApplicationSchema.methods.moveToNextStage = async function (): Promise<void> {
  if (this.currentStageIndex < this.interviewStages.length - 1) {
    this.currentStageIndex += 1;
    await this.save();
  }
};

jobApplicationSchema.methods.withdraw = async function (reason?: string): Promise<void> {
  this.status = 'withdrawn';
  this.withdrawnAt = new Date();
  if (reason) {
    this.reviewNotes = reason;
  }
  await this.save();
};

jobApplicationSchema.methods.reject = async function (
  reason: string,
  rejectedBy?: string
): Promise<void> {
  this.status = 'rejected';
  this.rejectedAt = new Date();
  this.rejectionReason = reason;
  if (rejectedBy) {
    this.reviewedBy = rejectedBy;
    this.reviewedAt = new Date();
  }
  await this.save();
};

jobApplicationSchema.methods.extendOffer = async function (): Promise<void> {
  this.status = 'offer_extended';
  this.offerExtendedAt = new Date();
  await this.save();
};

jobApplicationSchema.methods.acceptOffer = async function (): Promise<void> {
  this.status = 'offer_accepted';
  this.offerAcceptedAt = new Date();
  await this.save();
};

jobApplicationSchema.methods.declineOffer = async function (): Promise<void> {
  this.status = 'offer_declined';
  await this.save();
};

jobApplicationSchema.methods.getCurrentStage = function (): InterviewStage | null {
  if (this.interviewStages.length > this.currentStageIndex) {
    return this.interviewStages[this.currentStageIndex];
  }
  return null;
};

jobApplicationSchema.methods.getStatusHistory = function (): any[] {
  // This would typically be implemented with a separate StatusHistory collection
  // For now, return basic timeline based on timestamps
  const history = [];

  history.push({
    status: 'submitted',
    timestamp: this.submittedAt,
    updatedBy: this.applicantId,
  });

  if (this.reviewedAt) {
    history.push({
      status: 'under_review',
      timestamp: this.reviewedAt,
      updatedBy: this.reviewedBy,
    });
  }

  if (this.offerExtendedAt) {
    history.push({
      status: 'offer_extended',
      timestamp: this.offerExtendedAt,
      updatedBy: this.reviewedBy,
    });
  }

  if (this.offerAcceptedAt) {
    history.push({
      status: 'offer_accepted',
      timestamp: this.offerAcceptedAt,
      updatedBy: this.applicantId,
    });
  }

  if (this.rejectedAt) {
    history.push({
      status: 'rejected',
      timestamp: this.rejectedAt,
      updatedBy: this.reviewedBy,
      reason: this.rejectionReason,
    });
  }

  if (this.withdrawnAt) {
    history.push({
      status: 'withdrawn',
      timestamp: this.withdrawnAt,
      updatedBy: this.applicantId,
    });
  }

  return history.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
};

jobApplicationSchema.methods.canWithdraw = function (): boolean {
  const nonWithdrawableStatuses = ['offer_accepted', 'rejected', 'withdrawn'];
  return !nonWithdrawableStatuses.includes(this.status);
};

jobApplicationSchema.methods.canReject = function (): boolean {
  const nonRejectableStatuses = ['offer_accepted', 'rejected', 'withdrawn'];
  return !nonRejectableStatuses.includes(this.status);
};

jobApplicationSchema.methods.isActive = function (): boolean {
  const inactiveStatuses = ['offer_accepted', 'offer_declined', 'rejected', 'withdrawn'];
  return !inactiveStatuses.includes(this.status);
};

// Static methods
jobApplicationSchema.statics.createApplication = async function (
  applicationData: CreateJobApplicationInput
): Promise<IJobApplication> {
  // Validate input with Zod
  const validatedData = CreateJobApplicationSchema.parse(applicationData);

  // Check if user has already applied for this job
  const existingApplication = await this.findOne({
    jobId: validatedData.jobId,
    applicantId: validatedData.applicantId,
  });

  if (existingApplication) {
    throw new Error('User has already applied for this job');
  }

  // Create new application
  const application = new this(validatedData);
  await application.save();

  return application;
};

jobApplicationSchema.statics.findByApplicationId = async function (
  applicationId: string
): Promise<IJobApplication | null> {
  return this.findOne({ applicationId });
};

jobApplicationSchema.statics.findByJob = async function (
  jobId: string
): Promise<IJobApplication[]> {
  return this.find({ jobId }).sort({ submittedAt: -1 });
};

jobApplicationSchema.statics.findByApplicant = async function (
  applicantId: string
): Promise<IJobApplication[]> {
  return this.find({ applicantId }).sort({ submittedAt: -1 });
};

jobApplicationSchema.statics.findByStatus = async function (
  status: ApplicationStatus
): Promise<IJobApplication[]> {
  return this.find({ status }).sort({ submittedAt: -1 });
};

jobApplicationSchema.statics.findByOrganization = async function (
  organizationId: string
): Promise<IJobApplication[]> {
  // This requires joining with Job collection to filter by organization
  return this.aggregate([
    {
      $lookup: {
        from: 'jobs',
        localField: 'jobId',
        foreignField: 'jobId',
        as: 'job',
      },
    },
    {
      $match: {
        'job.organizationId': organizationId,
      },
    },
    {
      $sort: { submittedAt: -1 },
    },
  ]);
};

jobApplicationSchema.statics.getApplicationStats = async function (
  jobId?: string,
  organizationId?: string
): Promise<any> {
  const matchStage: any = {};

  if (jobId) {
    matchStage.jobId = jobId;
  } else if (organizationId) {
    // Need to join with jobs to filter by organization
    const pipeline = [
      {
        $lookup: {
          from: 'jobs',
          localField: 'jobId',
          foreignField: 'jobId',
          as: 'job',
        },
      },
      {
        $match: {
          'job.organizationId': organizationId,
        },
      },
      {
        $group: {
          _id: null,
          totalApplications: { $sum: 1 },
          byStatus: {
            $push: {
              status: '$status',
              count: 1,
            },
          },
          bySource: {
            $push: {
              source: '$source',
              count: 1,
            },
          },
          avgTimeToReview: {
            $avg: {
              $subtract: ['$reviewedAt', '$submittedAt'],
            },
          },
        },
      },
    ];

    const result = await this.aggregate(pipeline);
    return result[0] || { totalApplications: 0, byStatus: [], bySource: [], avgTimeToReview: 0 };
  }

  const pipeline = [
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalApplications: { $sum: 1 },
        submittedApplications: {
          $sum: { $cond: [{ $eq: ['$status', 'submitted'] }, 1, 0] },
        },
        underReviewApplications: {
          $sum: { $cond: [{ $eq: ['$status', 'under_review'] }, 1, 0] },
        },
        shortlistedApplications: {
          $sum: { $cond: [{ $eq: ['$status', 'shortlisted'] }, 1, 0] },
        },
        interviewScheduledApplications: {
          $sum: { $cond: [{ $eq: ['$status', 'interview_scheduled'] }, 1, 0] },
        },
        offerExtendedApplications: {
          $sum: { $cond: [{ $eq: ['$status', 'offer_extended'] }, 1, 0] },
        },
        offerAcceptedApplications: {
          $sum: { $cond: [{ $eq: ['$status', 'offer_accepted'] }, 1, 0] },
        },
        rejectedApplications: {
          $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] },
        },
        withdrawnApplications: {
          $sum: { $cond: [{ $eq: ['$status', 'withdrawn'] }, 1, 0] },
        },
        avgTimeToReview: {
          $avg: {
            $cond: [
              { $ne: ['$reviewedAt', null] },
              { $subtract: ['$reviewedAt', '$submittedAt'] },
              null,
            ],
          },
        },
      },
    },
  ];

  const result = await this.aggregate(pipeline);
  return (
    result[0] || {
      totalApplications: 0,
      submittedApplications: 0,
      underReviewApplications: 0,
      shortlistedApplications: 0,
      interviewScheduledApplications: 0,
      offerExtendedApplications: 0,
      offerAcceptedApplications: 0,
      rejectedApplications: 0,
      withdrawnApplications: 0,
      avgTimeToReview: 0,
    }
  );
};

jobApplicationSchema.statics.findPendingApplications = async function (
  organizationId?: string
): Promise<IJobApplication[]> {
  const pendingStatuses = ['submitted', 'under_review'];

  if (organizationId) {
    return this.aggregate([
      {
        $lookup: {
          from: 'jobs',
          localField: 'jobId',
          foreignField: 'jobId',
          as: 'job',
        },
      },
      {
        $match: {
          'job.organizationId': organizationId,
          status: { $in: pendingStatuses },
        },
      },
      {
        $sort: { submittedAt: 1 },
      },
    ]);
  }

  return this.find({ status: { $in: pendingStatuses } }).sort({ submittedAt: 1 });
};

jobApplicationSchema.statics.findApplicationsForReview = async function (
  organizationId?: string
): Promise<IJobApplication[]> {
  const reviewStatuses = ['submitted', 'under_review', 'shortlisted'];

  if (organizationId) {
    return this.aggregate([
      {
        $lookup: {
          from: 'jobs',
          localField: 'jobId',
          foreignField: 'jobId',
          as: 'job',
        },
      },
      {
        $match: {
          'job.organizationId': organizationId,
          status: { $in: reviewStatuses },
        },
      },
      {
        $sort: { submittedAt: 1 },
      },
    ]);
  }

  return this.find({ status: { $in: reviewStatuses } }).sort({ submittedAt: 1 });
};

// Create and export the model
export const JobApplication =
  (mongoose.models.JobApplication as IJobApplicationModel) ||
  mongoose.model<IJobApplication, IJobApplicationModel>('JobApplication', jobApplicationSchema);

// Helper functions for Next.js API routes
export class JobApplicationService {
  static async submitApplication(
    applicationData: CreateJobApplicationInput
  ): Promise<IJobApplication> {
    try {
      return await JobApplication.createApplication(applicationData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  static async getApplicationById(applicationId: string): Promise<IJobApplication | null> {
    return await JobApplication.findByApplicationId(applicationId);
  }

  static async updateApplication(
    applicationId: string,
    updateData: UpdateJobApplicationInput
  ): Promise<IJobApplication | null> {
    const validatedData = UpdateJobApplicationSchema.parse(updateData);
    return await JobApplication.findOneAndUpdate(
      { applicationId },
      { $set: validatedData },
      { new: true, runValidators: true }
    );
  }

  static async getJobApplications(jobId: string): Promise<IJobApplication[]> {
    return await JobApplication.findByJob(jobId);
  }

  static async getUserApplications(applicantId: string): Promise<IJobApplication[]> {
    return await JobApplication.findByApplicant(applicantId);
  }

  static async getApplicationsByStatus(status: ApplicationStatus): Promise<IJobApplication[]> {
    return await JobApplication.findByStatus(status);
  }

  static async getOrganizationApplications(organizationId: string): Promise<IJobApplication[]> {
    return await JobApplication.findByOrganization(organizationId);
  }

  static async updateApplicationStatus(
    applicationId: string,
    status: ApplicationStatus,
    updatedBy?: string,
    notes?: string
  ): Promise<IJobApplication | null> {
    const application = await JobApplication.findByApplicationId(applicationId);
    if (application) {
      await application.updateStatus(status, updatedBy, notes);
      return application;
    }
    return null;
  }

  static async withdrawApplication(
    applicationId: string,
    reason?: string
  ): Promise<IJobApplication | null> {
    const application = await JobApplication.findByApplicationId(applicationId);
    if (application && application.canWithdraw()) {
      await application.withdraw(reason);
      return application;
    }
    return null;
  }

  static async rejectApplication(
    applicationId: string,
    reason: string,
    rejectedBy?: string
  ): Promise<IJobApplication | null> {
    const application = await JobApplication.findByApplicationId(applicationId);
    if (application && application.canReject()) {
      await application.reject(reason, rejectedBy);
      return application;
    }
    return null;
  }

  static async scheduleInterview(
    applicationId: string,
    stageId: string,
    scheduledAt: Date,
    interviewerIds: string[]
  ): Promise<IJobApplication | null> {
    const application = await JobApplication.findByApplicationId(applicationId);
    if (application) {
      await application.scheduleInterview(stageId, scheduledAt, interviewerIds);
      return application;
    }
    return null;
  }

  static async completeInterviewStage(
    applicationId: string,
    stageId: string,
    feedback?: string,
    score?: number
  ): Promise<IJobApplication | null> {
    const application = await JobApplication.findByApplicationId(applicationId);
    if (application) {
      await application.completeInterviewStage(stageId, feedback, score);
      return application;
    }
    return null;
  }

  static async extendOffer(applicationId: string): Promise<IJobApplication | null> {
    const application = await JobApplication.findByApplicationId(applicationId);
    if (application) {
      await application.extendOffer();
      return application;
    }
    return null;
  }

  static async acceptOffer(applicationId: string): Promise<IJobApplication | null> {
    const application = await JobApplication.findByApplicationId(applicationId);
    if (application) {
      await application.acceptOffer();
      return application;
    }
    return null;
  }

  static async declineOffer(applicationId: string): Promise<IJobApplication | null> {
    const application = await JobApplication.findByApplicationId(applicationId);
    if (application) {
      await application.declineOffer();
      return application;
    }
    return null;
  }

  static async addDocument(
    applicationId: string,
    document: JobDocument
  ): Promise<IJobApplication | null> {
    const validatedDocument = DocumentSchema.parse(document);
    const application = await JobApplication.findByApplicationId(applicationId);
    if (application) {
      await application.addDocument(validatedDocument);
      return application;
    }
    return null;
  }

  static async removeDocument(
    applicationId: string,
    documentName: string
  ): Promise<IJobApplication | null> {
    const application = await JobApplication.findByApplicationId(applicationId);
    if (application) {
      await application.removeDocument(documentName);
      return application;
    }
    return null;
  }

  static async getApplicationStats(jobId?: string, organizationId?: string): Promise<any> {
    return await JobApplication.getApplicationStats(jobId, organizationId);
  }

  static async getPendingApplications(organizationId?: string): Promise<IJobApplication[]> {
    return await JobApplication.findPendingApplications(organizationId);
  }

  static async getApplicationsForReview(organizationId?: string): Promise<IJobApplication[]> {
    return await JobApplication.findApplicationsForReview(organizationId);
  }

  static async getApplicationStatusHistory(applicationId: string): Promise<any[]> {
    const application = await JobApplication.findByApplicationId(applicationId);
    return application ? application.getStatusHistory() : [];
  }

  static async getCurrentInterviewStage(applicationId: string): Promise<InterviewStage | null> {
    const application = await JobApplication.findByApplicationId(applicationId);
    return application ? application.getCurrentStage() : null;
  }

  static async deleteApplication(applicationId: string): Promise<boolean> {
    const result = await JobApplication.deleteOne({ applicationId });
    return result.deletedCount === 1;
  }

  // Bulk operations
  static async bulkUpdateStatus(
    applicationIds: string[],
    status: ApplicationStatus,
    updatedBy?: string
  ): Promise<number> {
    const result = await JobApplication.updateMany(
      { applicationId: { $in: applicationIds } },
      {
        $set: {
          status,
          lastUpdatedAt: new Date(),
          ...(updatedBy && { reviewedBy: updatedBy, reviewedAt: new Date() }),
        },
      }
    );
    return result.modifiedCount || 0;
  }

  static async getApplicationsWithFilters(filters: {
    jobId?: string;
    applicantId?: string;
    status?: ApplicationStatus[];
    source?: ApplicationSource[];
    organizationId?: string;
    submittedAfter?: Date;
    submittedBefore?: Date;
    limit?: number;
    offset?: number;
  }): Promise<IJobApplication[]> {
    const query: any = {};

    if (filters.jobId) query.jobId = filters.jobId;
    if (filters.applicantId) query.applicantId = filters.applicantId;
    if (filters.status?.length) query.status = { $in: filters.status };
    if (filters.source?.length) query.source = { $in: filters.source };

    if (filters.submittedAfter || filters.submittedBefore) {
      query.submittedAt = {};
      if (filters.submittedAfter) query.submittedAt.$gte = filters.submittedAfter;
      if (filters.submittedBefore) query.submittedAt.$lte = filters.submittedBefore;
    }

    if (filters.organizationId) {
      // Use aggregation to filter by organization
      return JobApplication.aggregate([
        {
          $lookup: {
            from: 'jobs',
            localField: 'jobId',
            foreignField: 'jobId',
            as: 'job',
          },
        },
        {
          $match: {
            'job.organizationId': filters.organizationId,
            ...query,
          },
        },
        {
          $sort: { submittedAt: -1 },
        },
        ...(filters.offset ? [{ $skip: filters.offset }] : []),
        ...(filters.limit ? [{ $limit: filters.limit }] : []),
      ]);
    }

    return JobApplication.find(query)
      .sort({ submittedAt: -1 })
      .limit(filters.limit || 50)
      .skip(filters.offset || 0);
  }
}
