import mongoose, { Document, Model, Schema } from 'mongoose';
import { nanoid } from 'nanoid';
import { z } from 'zod';

// Zod validation schemas
export const OrganizationTypeSchema = z.enum([
  'startup',
  'small_business',
  'medium_business',
  'enterprise',
  'non_profit',
  'government',
]);
export const OrganizationStatusSchema = z.enum([
  'active',
  'inactive',
  'suspended',
  'pending_verification',
]);
export const IndustrySchema = z.enum([
  'technology',
  'healthcare',
  'finance',
  'education',
  'retail',
  'manufacturing',
  'consulting',
  'media',
  'real_estate',
  'transportation',
  'energy',
  'agriculture',
  'hospitality',
  'construction',
  'telecommunications',
  'automotive',
  'aerospace',
  'biotechnology',
  'pharmaceuticals',
  'entertainment',
  'sports',
  'other',
]);

export const AddressSchema = z.object({
  street: z.string().min(1, 'Street address is required'),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  country: z.string().min(1, 'Country is required'),
  postalCode: z.string().min(1, 'Postal code is required'),
});

export const SocialLinksSchema = z.object({
  website: z.string().url().optional(),
  linkedin: z.string().url().optional(),
  twitter: z.string().url().optional(),
  facebook: z.string().url().optional(),
  instagram: z.string().url().optional(),
});

export const OrganizationSettingsSchema = z.object({
  allowPublicJobPostings: z.boolean().default(true),
  requireApprovalForJobPostings: z.boolean().default(false),
  enableAiInterviews: z.boolean().default(true),
  defaultInterviewDuration: z.number().min(15).max(180).default(60), // minutes
  timezone: z.string().default('UTC'),
  currency: z.string().length(3).default('USD'),
  language: z.string().default('en'),
  emailNotifications: z.boolean().default(true),
  smsNotifications: z.boolean().default(false),
});

export const CreateOrganizationSchema = z.object({
  name: z.string().min(1, 'Organization name is required'),
  description: z.string().optional(),
  type: OrganizationTypeSchema,
  industry: IndustrySchema,
  size: z.string().optional(),
  foundedYear: z.number().min(1800).max(new Date().getFullYear()).optional(),
  address: AddressSchema.optional(),
  phone: z.string().optional(),
  email: z.string().email('Invalid email format').optional(),
  logo: z.string().url().optional(),
  socialLinks: SocialLinksSchema.optional(),
  settings: OrganizationSettingsSchema.optional(),
  ownerId: z.string().min(1, 'Owner ID is required'),
});

export const UpdateOrganizationSchema = CreateOrganizationSchema.partial().omit({ ownerId: true });

// TypeScript interfaces
export type OrganizationType = z.infer<typeof OrganizationTypeSchema>;
export type OrganizationStatus = z.infer<typeof OrganizationStatusSchema>;
export type Industry = z.infer<typeof IndustrySchema>;
export type Address = z.infer<typeof AddressSchema>;
export type SocialLinks = z.infer<typeof SocialLinksSchema>;
export type OrganizationSettings = z.infer<typeof OrganizationSettingsSchema>;
export type CreateOrganizationInput = z.infer<typeof CreateOrganizationSchema>;
export type UpdateOrganizationInput = z.infer<typeof UpdateOrganizationSchema>;

export interface IOrganization extends Document {
  _id: mongoose.Types.ObjectId;
  organizationId: string;
  name: string;
  slug: string;
  description?: string;
  type: OrganizationType;
  industry: Industry;
  size?: string;
  foundedYear?: number;
  address?: Address;
  phone?: string;
  email?: string;
  logo?: string;
  socialLinks?: SocialLinks;
  settings: OrganizationSettings;
  status: OrganizationStatus;
  ownerId: string;
  memberCount: number;
  jobCount: number;
  isVerified: boolean;
  verifiedAt?: Date;
  subscriptionPlanId?: string;
  subscriptionStatus?: string;
  trialEndsAt?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  generateSlug(): string;
  updateMemberCount(): Promise<number>;
  updateJobCount(): Promise<number>;
  isTrialActive(): boolean;
  canCreateJobs(): boolean;
  canUseAiInterviews(): boolean;
  getPublicProfile(): Partial<IOrganization>;
}

// Static methods interface
export interface IOrganizationModel extends Model<IOrganization> {
  createOrganization(orgData: CreateOrganizationInput): Promise<IOrganization>;
  findBySlug(slug: string): Promise<IOrganization | null>;
  findByOrganizationId(organizationId: string): Promise<IOrganization | null>;
  findByOwner(ownerId: string): Promise<IOrganization[]>;
  findByIndustry(industry: Industry): Promise<IOrganization[]>;
  findActiveOrganizations(): Promise<IOrganization[]>;
  searchOrganizations(query: string): Promise<IOrganization[]>;
}

// Mongoose schema
const organizationSchema = new Schema<IOrganization>(
  {
    organizationId: {
      type: String,
      unique: true,
      default: () => `org_${nanoid(12)}`,
      index: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
      index: true,
    },
    slug: {
      type: String,
      unique: true,
      index: true,
    },
    description: {
      type: String,
      trim: true,
    },
    type: {
      type: String,
      enum: [
        'startup',
        'small_business',
        'medium_business',
        'enterprise',
        'non_profit',
        'government',
      ],
      required: true,
      index: true,
    },
    industry: {
      type: String,
      enum: [
        'technology',
        'healthcare',
        'finance',
        'education',
        'retail',
        'manufacturing',
        'consulting',
        'media',
        'real_estate',
        'transportation',
        'energy',
        'agriculture',
        'hospitality',
        'construction',
        'telecommunications',
        'automotive',
        'aerospace',
        'biotechnology',
        'pharmaceuticals',
        'entertainment',
        'sports',
        'other',
      ],
      required: true,
      index: true,
    },
    size: {
      type: String,
    },
    foundedYear: {
      type: Number,
      min: 1800,
      max: new Date().getFullYear(),
    },
    address: {
      street: String,
      city: String,
      state: String,
      country: String,
      postalCode: String,
    },
    phone: {
      type: String,
      trim: true,
    },
    email: {
      type: String,
      lowercase: true,
      trim: true,
    },
    logo: {
      type: String,
    },
    socialLinks: {
      website: String,
      linkedin: String,
      twitter: String,
      facebook: String,
      instagram: String,
    },
    settings: {
      allowPublicJobPostings: {
        type: Boolean,
        default: true,
      },
      requireApprovalForJobPostings: {
        type: Boolean,
        default: false,
      },
      enableAiInterviews: {
        type: Boolean,
        default: true,
      },
      defaultInterviewDuration: {
        type: Number,
        min: 15,
        max: 180,
        default: 60,
      },
      timezone: {
        type: String,
        default: 'UTC',
      },
      currency: {
        type: String,
        length: 3,
        default: 'USD',
      },
      language: {
        type: String,
        default: 'en',
      },
      emailNotifications: {
        type: Boolean,
        default: true,
      },
      smsNotifications: {
        type: Boolean,
        default: false,
      },
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended', 'pending_verification'],
      default: 'pending_verification',
      index: true,
    },
    ownerId: {
      type: String,
      required: true,
      index: true,
    },
    memberCount: {
      type: Number,
      default: 1,
    },
    jobCount: {
      type: Number,
      default: 0,
    },
    isVerified: {
      type: Boolean,
      default: false,
      index: true,
    },
    verifiedAt: {
      type: Date,
    },
    subscriptionPlanId: {
      type: String,
      index: true,
    },
    subscriptionStatus: {
      type: String,
      index: true,
    },
    trialEndsAt: {
      type: Date,
      index: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes
organizationSchema.index({ name: 'text', description: 'text' });
organizationSchema.index({ createdAt: -1 });

// Pre-save middleware to generate slug
organizationSchema.pre('save', function (next) {
  if (this.isModified('name') || this.isNew) {
    this.slug = this.generateSlug();
  }
  next();
});

// Instance methods
organizationSchema.methods.generateSlug = function (): string {
  const baseSlug = this.name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');

  // Add random suffix to ensure uniqueness
  return `${baseSlug}-${nanoid(6)}`;
};

organizationSchema.methods.updateMemberCount = async function (): Promise<number> {
  // This will be implemented when we create the OrganizationMember model
  // For now, return the current count
  return this.memberCount;
};

organizationSchema.methods.updateJobCount = async function (): Promise<number> {
  // This will be implemented when we create the Job model
  // For now, return the current count
  return this.jobCount;
};

organizationSchema.methods.isTrialActive = function (): boolean {
  return !!(this.trialEndsAt && this.trialEndsAt > new Date());
};

organizationSchema.methods.canCreateJobs = function (): boolean {
  return this.status === 'active' && (this.subscriptionStatus === 'active' || this.isTrialActive());
};

organizationSchema.methods.canUseAiInterviews = function (): boolean {
  return this.settings.enableAiInterviews && this.canCreateJobs();
};

organizationSchema.methods.getPublicProfile = function (): Partial<IOrganization> {
  return {
    organizationId: this.organizationId,
    name: this.name,
    slug: this.slug,
    description: this.description,
    type: this.type,
    industry: this.industry,
    size: this.size,
    foundedYear: this.foundedYear,
    logo: this.logo,
    socialLinks: this.socialLinks,
    isVerified: this.isVerified,
    memberCount: this.memberCount,
    jobCount: this.jobCount,
  };
};

// Static methods
organizationSchema.statics.createOrganization = async function (
  orgData: CreateOrganizationInput
): Promise<IOrganization> {
  // Validate input with Zod
  const validatedData = CreateOrganizationSchema.parse(orgData);

  // Create new organization with default settings
  const organization = new this({
    ...validatedData,
    settings: {
      ...OrganizationSettingsSchema.parse({}), // Apply defaults
      ...validatedData.settings,
    },
    trialEndsAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days trial
  });

  await organization.save();
  return organization;
};

organizationSchema.statics.findBySlug = async function (
  slug: string
): Promise<IOrganization | null> {
  return this.findOne({ slug });
};

organizationSchema.statics.findByOrganizationId = async function (
  organizationId: string
): Promise<IOrganization | null> {
  return this.findOne({ organizationId });
};

organizationSchema.statics.findByOwner = async function (
  ownerId: string
): Promise<IOrganization[]> {
  return this.find({ ownerId }).sort({ createdAt: -1 });
};

organizationSchema.statics.findByIndustry = async function (
  industry: Industry
): Promise<IOrganization[]> {
  return this.find({ industry, status: 'active' }).sort({ createdAt: -1 });
};

organizationSchema.statics.findActiveOrganizations = async function (): Promise<IOrganization[]> {
  return this.find({ status: 'active' }).sort({ createdAt: -1 });
};

organizationSchema.statics.searchOrganizations = async function (
  query: string
): Promise<IOrganization[]> {
  return this.find({
    $text: { $search: query },
    status: 'active',
  }).sort({ score: { $meta: 'textScore' } });
};

// Create and export the model
export const Organization =
  (mongoose.models.Organization as IOrganizationModel) ||
  mongoose.model<IOrganization, IOrganizationModel>('Organization', organizationSchema);

// Helper functions for Next.js API routes
export class OrganizationService {
  static async createOrganization(orgData: CreateOrganizationInput): Promise<IOrganization> {
    try {
      return await Organization.createOrganization(orgData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  static async getOrganizationById(organizationId: string): Promise<IOrganization | null> {
    return await Organization.findByOrganizationId(organizationId);
  }

  static async getOrganizationBySlug(slug: string): Promise<IOrganization | null> {
    return await Organization.findBySlug(slug);
  }

  static async updateOrganization(
    organizationId: string,
    updateData: UpdateOrganizationInput
  ): Promise<IOrganization | null> {
    const validatedData = UpdateOrganizationSchema.parse(updateData);
    return await Organization.findOneAndUpdate(
      { organizationId },
      { $set: validatedData },
      { new: true, runValidators: true }
    );
  }

  static async getOrganizationsByOwner(ownerId: string): Promise<IOrganization[]> {
    return await Organization.findByOwner(ownerId);
  }

  static async searchOrganizations(query: string): Promise<IOrganization[]> {
    if (!query.trim()) {
      return [];
    }
    return await Organization.searchOrganizations(query);
  }

  static async getOrganizationsByIndustry(industry: Industry): Promise<IOrganization[]> {
    return await Organization.findByIndustry(industry);
  }

  static async verifyOrganization(organizationId: string): Promise<IOrganization | null> {
    return await Organization.findOneAndUpdate(
      { organizationId },
      {
        $set: {
          isVerified: true,
          verifiedAt: new Date(),
          status: 'active',
        },
      },
      { new: true }
    );
  }

  static async suspendOrganization(
    organizationId: string,
    reason?: string
  ): Promise<IOrganization | null> {
    return await Organization.findOneAndUpdate(
      { organizationId },
      {
        $set: {
          status: 'suspended',
          suspendedAt: new Date(),
          suspensionReason: reason,
        },
      },
      { new: true }
    );
  }

  static async updateSettings(
    organizationId: string,
    settings: Partial<OrganizationSettings>
  ): Promise<IOrganization | null> {
    const validatedSettings = OrganizationSettingsSchema.partial().parse(settings);
    return await Organization.findOneAndUpdate(
      { organizationId },
      { $set: { settings: validatedSettings } },
      { new: true }
    );
  }

  static async getPublicProfile(organizationId: string): Promise<Partial<IOrganization> | null> {
    const organization = await Organization.findByOrganizationId(organizationId);
    return organization ? organization.getPublicProfile() : null;
  }

  static async deleteOrganization(organizationId: string): Promise<boolean> {
    const result = await Organization.deleteOne({ organizationId });
    return result.deletedCount === 1;
  }
}
