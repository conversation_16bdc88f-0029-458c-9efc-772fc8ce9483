import mongoose, { Document, Model, Schema } from 'mongoose';
import { nanoid } from 'nanoid';
import { z } from 'zod';

// Zod validation schemas
export const SubscriptionEventTypeSchema = z.enum([
  'subscription_created',
  'subscription_updated',
  'subscription_cancelled',
  'subscription_reactivated',
  'subscription_expired',
  'subscription_paused',
  'subscription_resumed',
  'plan_changed',
  'plan_upgraded',
  'plan_downgraded',
  'payment_succeeded',
  'payment_failed',
  'payment_pending',
  'payment_refunded',
  'payment_disputed',
  'invoice_created',
  'invoice_paid',
  'invoice_failed',
  'trial_started',
  'trial_ended',
  'trial_extended',
  'discount_applied',
  'discount_removed',
  'webhook_received',
  'manual_adjustment',
]);

export const PaymentStatusSchema = z.enum([
  'pending',
  'processing',
  'succeeded',
  'failed',
  'cancelled',
  'refunded',
  'disputed',
  'partially_refunded',
]);

export const PaymentMethodSchema = z.enum([
  'card',
  'bank_transfer',
  'wallet',
  'upi',
  'net_banking',
  'emi',
  'other',
]);

export const PaymentDetailsSchema = z.object({
  amount: z.number().min(0),
  currency: z.string().length(3).default('USD'),
  paymentMethod: PaymentMethodSchema.optional(),
  paymentMethodDetails: z.record(z.unknown()).optional(),
  transactionId: z.string().optional(),
  invoiceId: z.string().optional(),
  receiptUrl: z.string().url().optional(),
  refundAmount: z.number().min(0).optional(),
  refundReason: z.string().optional(),
  failureReason: z.string().optional(),
  processingFee: z.number().min(0).optional(),
  netAmount: z.number().min(0).optional(),
});

export const SubscriptionDetailsSchema = z.object({
  subscriptionId: z.string(),
  organizationId: z.string(),
  userId: z.string(),
  planId: z.string(),
  previousPlanId: z.string().optional(),
  status: z.string(),
  previousStatus: z.string().optional(),
  billingInterval: z.enum(['monthly', 'quarterly', 'yearly', 'lifetime']),
  currentPeriodStart: z.date(),
  currentPeriodEnd: z.date(),
  trialStart: z.date().optional(),
  trialEnd: z.date().optional(),
  cancelledAt: z.date().optional(),
  cancelReason: z.string().optional(),
});

export const CreateSubscriptionLogSchema = z.object({
  eventType: SubscriptionEventTypeSchema,
  subscriptionDetails: SubscriptionDetailsSchema,
  paymentDetails: PaymentDetailsSchema.optional(),
  paymentProvider: z.enum(['stripe', 'razorpay', 'polar']),
  providerEventId: z.string().optional(),
  providerSubscriptionId: z.string().optional(),
  providerCustomerId: z.string().optional(),
  webhookData: z.record(z.unknown()).optional(),
  metadata: z.record(z.unknown()).optional(),
  notes: z.string().optional(),
  processedBy: z.string().optional(), // User ID who processed this manually
  isManual: z.boolean().default(false),
});

// TypeScript interfaces
export type SubscriptionEventType = z.infer<typeof SubscriptionEventTypeSchema>;
export type PaymentStatus = z.infer<typeof PaymentStatusSchema>;
export type PaymentMethod = z.infer<typeof PaymentMethodSchema>;
export type PaymentDetails = z.infer<typeof PaymentDetailsSchema>;
export type SubscriptionDetails = z.infer<typeof SubscriptionDetailsSchema>;
export type CreateSubscriptionLogInput = z.infer<typeof CreateSubscriptionLogSchema>;

export interface ISubscriptionLog extends Document {
  _id: mongoose.Types.ObjectId;
  logId: string;
  eventType: SubscriptionEventType;
  subscriptionDetails: SubscriptionDetails;
  paymentDetails?: PaymentDetails;
  paymentProvider: 'stripe' | 'razorpay' | 'polar';
  providerEventId?: string;
  providerSubscriptionId?: string;
  providerCustomerId?: string;
  webhookData?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
  notes?: string;
  processedBy?: string;
  isManual: boolean;
  processedAt: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  isPaymentEvent(): boolean;
  isSubscriptionEvent(): boolean;
  getEventSummary(): string;
  getAmountFormatted(): string;
  isSuccessfulPayment(): boolean;
  isFailedPayment(): boolean;
  getRelatedLogs(): Promise<ISubscriptionLog[]>;
}

// Static methods interface
export interface ISubscriptionLogModel extends Model<ISubscriptionLog> {
  createLog(logData: CreateSubscriptionLogInput): Promise<ISubscriptionLog>;
  findBySubscription(subscriptionId: string): Promise<ISubscriptionLog[]>;
  findByOrganization(organizationId: string): Promise<ISubscriptionLog[]>;
  findByUser(userId: string): Promise<ISubscriptionLog[]>;
  findByEventType(eventType: SubscriptionEventType): Promise<ISubscriptionLog[]>;
  findByProvider(provider: 'stripe' | 'razorpay' | 'polar'): Promise<ISubscriptionLog[]>;
  findPaymentLogs(organizationId?: string): Promise<ISubscriptionLog[]>;
  findFailedPayments(organizationId?: string): Promise<ISubscriptionLog[]>;
  getRevenueStats(startDate: Date, endDate: Date, organizationId?: string): Promise<any>;
  getSubscriptionStats(organizationId?: string): Promise<any>;
  findByProviderEventId(providerEventId: string): Promise<ISubscriptionLog | null>;
}

// Mongoose schema
const subscriptionLogSchema = new Schema<ISubscriptionLog>(
  {
    logId: {
      type: String,
      unique: true,
      default: () => `log_${nanoid(12)}`,
      index: true,
    },
    eventType: {
      type: String,
      enum: [
        'subscription_created',
        'subscription_updated',
        'subscription_cancelled',
        'subscription_reactivated',
        'subscription_expired',
        'subscription_paused',
        'subscription_resumed',
        'plan_changed',
        'plan_upgraded',
        'plan_downgraded',
        'payment_succeeded',
        'payment_failed',
        'payment_pending',
        'payment_refunded',
        'payment_disputed',
        'invoice_created',
        'invoice_paid',
        'invoice_failed',
        'trial_started',
        'trial_ended',
        'trial_extended',
        'discount_applied',
        'discount_removed',
        'webhook_received',
        'manual_adjustment',
      ],
      required: true,
      index: true,
    },
    subscriptionDetails: {
      subscriptionId: { type: String, required: true, index: true },
      organizationId: { type: String, required: true, index: true },
      userId: { type: String, required: true, index: true },
      planId: { type: String, required: true, index: true },
      previousPlanId: String,
      status: { type: String, required: true },
      previousStatus: String,
      billingInterval: {
        type: String,
        enum: ['monthly', 'quarterly', 'yearly', 'lifetime'],
        required: true,
      },
      currentPeriodStart: { type: Date, required: true },
      currentPeriodEnd: { type: Date, required: true },
      trialStart: Date,
      trialEnd: Date,
      cancelledAt: Date,
      cancelReason: String,
    },
    paymentDetails: {
      amount: { type: Number, min: 0 },
      currency: { type: String, default: 'USD' },
      paymentMethod: {
        type: String,
        enum: ['card', 'bank_transfer', 'wallet', 'upi', 'net_banking', 'emi', 'other'],
      },
      paymentMethodDetails: Schema.Types.Mixed,
      transactionId: String,
      invoiceId: String,
      receiptUrl: String,
      refundAmount: { type: Number, min: 0 },
      refundReason: String,
      failureReason: String,
      processingFee: { type: Number, min: 0 },
      netAmount: { type: Number, min: 0 },
    },
    paymentProvider: {
      type: String,
      enum: ['stripe', 'razorpay', 'polar'],
      required: true,
      index: true,
    },
    providerEventId: {
      type: String,
      index: true,
    },
    providerSubscriptionId: {
      type: String,
      index: true,
    },
    providerCustomerId: {
      type: String,
      index: true,
    },
    webhookData: {
      type: Schema.Types.Mixed,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    notes: {
      type: String,
    },
    processedBy: {
      type: String,
      index: true,
    },
    isManual: {
      type: Boolean,
      default: false,
      index: true,
    },
    processedAt: {
      type: Date,
      default: Date.now,
      index: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes
subscriptionLogSchema.index({ 'subscriptionDetails.subscriptionId': 1, createdAt: -1 });
subscriptionLogSchema.index({ 'subscriptionDetails.organizationId': 1, createdAt: -1 });
subscriptionLogSchema.index({ 'subscriptionDetails.userId': 1, createdAt: -1 });
subscriptionLogSchema.index({ eventType: 1, createdAt: -1 });
subscriptionLogSchema.index({ paymentProvider: 1, createdAt: -1 });
subscriptionLogSchema.index({ processedAt: -1 });
subscriptionLogSchema.index({ isManual: 1, processedAt: -1 });

// Instance methods
subscriptionLogSchema.methods.isPaymentEvent = function (): boolean {
  const paymentEvents = [
    'payment_succeeded',
    'payment_failed',
    'payment_pending',
    'payment_refunded',
    'payment_disputed',
    'invoice_created',
    'invoice_paid',
    'invoice_failed',
  ];
  return paymentEvents.includes(this.eventType);
};

subscriptionLogSchema.methods.isSubscriptionEvent = function (): boolean {
  const subscriptionEvents = [
    'subscription_created',
    'subscription_updated',
    'subscription_cancelled',
    'subscription_reactivated',
    'subscription_expired',
    'subscription_paused',
    'subscription_resumed',
    'plan_changed',
    'plan_upgraded',
    'plan_downgraded',
  ];
  return subscriptionEvents.includes(this.eventType);
};

subscriptionLogSchema.methods.getEventSummary = function (): string {
  const eventDescriptions: Record<string, string> = {
    subscription_created: 'Subscription created',
    subscription_updated: 'Subscription updated',
    subscription_cancelled: 'Subscription cancelled',
    subscription_reactivated: 'Subscription reactivated',
    subscription_expired: 'Subscription expired',
    subscription_paused: 'Subscription paused',
    subscription_resumed: 'Subscription resumed',
    plan_changed: 'Plan changed',
    plan_upgraded: 'Plan upgraded',
    plan_downgraded: 'Plan downgraded',
    payment_succeeded: 'Payment successful',
    payment_failed: 'Payment failed',
    payment_pending: 'Payment pending',
    payment_refunded: 'Payment refunded',
    payment_disputed: 'Payment disputed',
    invoice_created: 'Invoice created',
    invoice_paid: 'Invoice paid',
    invoice_failed: 'Invoice payment failed',
    trial_started: 'Trial started',
    trial_ended: 'Trial ended',
    trial_extended: 'Trial extended',
    discount_applied: 'Discount applied',
    discount_removed: 'Discount removed',
    webhook_received: 'Webhook received',
    manual_adjustment: 'Manual adjustment',
  };

  return eventDescriptions[this.eventType] || this.eventType;
};

subscriptionLogSchema.methods.getAmountFormatted = function (): string {
  if (!this.paymentDetails?.amount) return 'N/A';

  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: this.paymentDetails.currency || 'USD',
  });

  return formatter.format(this.paymentDetails.amount);
};

subscriptionLogSchema.methods.isSuccessfulPayment = function (): boolean {
  return this.eventType === 'payment_succeeded' || this.eventType === 'invoice_paid';
};

subscriptionLogSchema.methods.isFailedPayment = function (): boolean {
  return this.eventType === 'payment_failed' || this.eventType === 'invoice_failed';
};

subscriptionLogSchema.methods.getRelatedLogs = async function (): Promise<ISubscriptionLog[]> {
  return (this.constructor as ISubscriptionLogModel)
    .find({
      'subscriptionDetails.subscriptionId': this.subscriptionDetails.subscriptionId,
      _id: { $ne: this._id },
    })
    .sort({ createdAt: -1 })
    .limit(10);
};

// Static methods
subscriptionLogSchema.statics.createLog = async function (
  logData: CreateSubscriptionLogInput
): Promise<ISubscriptionLog> {
  // Validate input with Zod
  const validatedData = CreateSubscriptionLogSchema.parse(logData);

  // Create new log
  const log = new this(validatedData);
  await log.save();

  return log;
};

subscriptionLogSchema.statics.findBySubscription = async function (
  subscriptionId: string
): Promise<ISubscriptionLog[]> {
  return this.find({ 'subscriptionDetails.subscriptionId': subscriptionId }).sort({
    createdAt: -1,
  });
};

subscriptionLogSchema.statics.findByOrganization = async function (
  organizationId: string
): Promise<ISubscriptionLog[]> {
  return this.find({ 'subscriptionDetails.organizationId': organizationId }).sort({
    createdAt: -1,
  });
};

subscriptionLogSchema.statics.findByUser = async function (
  userId: string
): Promise<ISubscriptionLog[]> {
  return this.find({ 'subscriptionDetails.userId': userId }).sort({ createdAt: -1 });
};

subscriptionLogSchema.statics.findByEventType = async function (
  eventType: SubscriptionEventType
): Promise<ISubscriptionLog[]> {
  return this.find({ eventType }).sort({ createdAt: -1 });
};

subscriptionLogSchema.statics.findByProvider = async function (
  provider: 'stripe' | 'razorpay' | 'polar'
): Promise<ISubscriptionLog[]> {
  return this.find({ paymentProvider: provider }).sort({ createdAt: -1 });
};

subscriptionLogSchema.statics.findPaymentLogs = async function (
  organizationId?: string
): Promise<ISubscriptionLog[]> {
  const paymentEvents = [
    'payment_succeeded',
    'payment_failed',
    'payment_pending',
    'payment_refunded',
    'payment_disputed',
    'invoice_created',
    'invoice_paid',
    'invoice_failed',
  ];

  const query: any = { eventType: { $in: paymentEvents } };
  if (organizationId) {
    query['subscriptionDetails.organizationId'] = organizationId;
  }

  return this.find(query).sort({ createdAt: -1 });
};

subscriptionLogSchema.statics.findFailedPayments = async function (
  organizationId?: string
): Promise<ISubscriptionLog[]> {
  const failedEvents = ['payment_failed', 'invoice_failed'];

  const query: any = { eventType: { $in: failedEvents } };
  if (organizationId) {
    query['subscriptionDetails.organizationId'] = organizationId;
  }

  return this.find(query).sort({ createdAt: -1 });
};

subscriptionLogSchema.statics.getRevenueStats = async function (
  startDate: Date,
  endDate: Date,
  organizationId?: string
): Promise<any> {
  const matchStage: any = {
    eventType: { $in: ['payment_succeeded', 'invoice_paid'] },
    createdAt: { $gte: startDate, $lte: endDate },
  };

  if (organizationId) {
    matchStage['subscriptionDetails.organizationId'] = organizationId;
  }

  const pipeline = [
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$paymentDetails.amount' },
        totalTransactions: { $sum: 1 },
        avgTransactionValue: { $avg: '$paymentDetails.amount' },
        byProvider: {
          $push: {
            provider: '$paymentProvider',
            amount: '$paymentDetails.amount',
          },
        },
        byCurrency: {
          $push: {
            currency: '$paymentDetails.currency',
            amount: '$paymentDetails.amount',
          },
        },
      },
    },
  ];

  const result = await this.aggregate(pipeline);

  // Get monthly breakdown
  const monthlyPipeline = [
    { $match: matchStage },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
        },
        revenue: { $sum: '$paymentDetails.amount' },
        transactions: { $sum: 1 },
      },
    },
    { $sort: { '_id.year': 1, '_id.month': 1 } },
  ];

  const monthlyResult = await this.aggregate(monthlyPipeline as any[]);

  return {
    summary: result[0] || {
      totalRevenue: 0,
      totalTransactions: 0,
      avgTransactionValue: 0,
      byProvider: [],
      byCurrency: [],
    },
    monthly: monthlyResult,
  };
};

subscriptionLogSchema.statics.getSubscriptionStats = async function (
  organizationId?: string
): Promise<any> {
  const matchStage: any = {};
  if (organizationId) {
    matchStage['subscriptionDetails.organizationId'] = organizationId;
  }

  const pipeline = [
    { $match: matchStage },
    {
      $group: {
        _id: '$eventType',
        count: { $sum: 1 },
        latestEvent: { $max: '$createdAt' },
      },
    },
    { $sort: { count: -1 } },
  ];

  const eventStats = await this.aggregate(pipeline as any[]);

  // Get subscription lifecycle stats
  const lifecyclePipeline = [
    { $match: matchStage },
    {
      $group: {
        _id: '$subscriptionDetails.subscriptionId',
        events: { $push: '$eventType' },
        firstEvent: { $min: '$createdAt' },
        lastEvent: { $max: '$createdAt' },
        totalEvents: { $sum: 1 },
      },
    },
    {
      $group: {
        _id: null,
        totalSubscriptions: { $sum: 1 },
        avgEventsPerSubscription: { $avg: '$totalEvents' },
        avgLifecycleDays: {
          $avg: {
            $divide: [
              { $subtract: ['$lastEvent', '$firstEvent'] },
              1000 * 60 * 60 * 24, // Convert to days
            ],
          },
        },
      },
    },
  ];

  const lifecycleStats = await this.aggregate(lifecyclePipeline);

  return {
    eventStats,
    lifecycle: lifecycleStats[0] || {
      totalSubscriptions: 0,
      avgEventsPerSubscription: 0,
      avgLifecycleDays: 0,
    },
  };
};

subscriptionLogSchema.statics.findByProviderEventId = async function (
  providerEventId: string
): Promise<ISubscriptionLog | null> {
  return this.findOne({ providerEventId });
};

// Create and export the model
export const SubscriptionLog =
  (mongoose.models.SubscriptionLog as ISubscriptionLogModel) ||
  mongoose.model<ISubscriptionLog, ISubscriptionLogModel>('SubscriptionLog', subscriptionLogSchema);

// Helper functions for Next.js API routes
export class SubscriptionLogService {
  static async createLog(logData: CreateSubscriptionLogInput): Promise<ISubscriptionLog> {
    try {
      return await SubscriptionLog.createLog(logData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  static async getSubscriptionLogs(subscriptionId: string): Promise<ISubscriptionLog[]> {
    return await SubscriptionLog.findBySubscription(subscriptionId);
  }

  static async getOrganizationLogs(organizationId: string): Promise<ISubscriptionLog[]> {
    return await SubscriptionLog.findByOrganization(organizationId);
  }

  static async getUserLogs(userId: string): Promise<ISubscriptionLog[]> {
    return await SubscriptionLog.findByUser(userId);
  }

  static async getPaymentLogs(organizationId?: string): Promise<ISubscriptionLog[]> {
    return await SubscriptionLog.findPaymentLogs(organizationId);
  }

  static async getFailedPayments(organizationId?: string): Promise<ISubscriptionLog[]> {
    return await SubscriptionLog.findFailedPayments(organizationId);
  }

  static async getRevenueStats(
    startDate: Date,
    endDate: Date,
    organizationId?: string
  ): Promise<any> {
    return await SubscriptionLog.getRevenueStats(startDate, endDate, organizationId);
  }

  static async getSubscriptionStats(organizationId?: string): Promise<any> {
    return await SubscriptionLog.getSubscriptionStats(organizationId);
  }

  static async findByProviderEventId(providerEventId: string): Promise<ISubscriptionLog | null> {
    return await SubscriptionLog.findByProviderEventId(providerEventId);
  }

  // Webhook processing methods
  static async processStripeWebhook(
    eventData: any,
    subscriptionId: string,
    organizationId: string,
    userId: string
  ): Promise<ISubscriptionLog> {
    const eventType = this.mapStripeEventType(eventData.type);

    const logData: CreateSubscriptionLogInput = {
      eventType,
      subscriptionDetails: {
        subscriptionId,
        organizationId,
        userId,
        planId: eventData.data?.object?.items?.data?.[0]?.price?.id || 'unknown',
        status: eventData.data?.object?.status || 'unknown',
        billingInterval: this.mapStripeBillingInterval(
          eventData.data?.object?.items?.data?.[0]?.price?.recurring?.interval
        ),
        currentPeriodStart: new Date(eventData.data?.object?.current_period_start * 1000),
        currentPeriodEnd: new Date(eventData.data?.object?.current_period_end * 1000),
      },
      paymentProvider: 'stripe',
      providerEventId: eventData.id,
      providerSubscriptionId: eventData.data?.object?.id,
      providerCustomerId: eventData.data?.object?.customer,
      webhookData: eventData,
      isManual: false,
    };

    // Add payment details if it's a payment event
    if (eventData.data?.object?.amount_paid) {
      logData.paymentDetails = {
        amount: eventData.data.object.amount_paid / 100, // Convert from cents
        currency: eventData.data.object.currency?.toUpperCase() || 'USD',
        transactionId: eventData.data.object.payment_intent,
        invoiceId: eventData.data.object.id,
      };
    }

    return await this.createLog(logData);
  }

  static async processRazorpayWebhook(
    eventData: any,
    subscriptionId: string,
    organizationId: string,
    userId: string
  ): Promise<ISubscriptionLog> {
    const eventType = this.mapRazorpayEventType(eventData.event);

    const logData: CreateSubscriptionLogInput = {
      eventType,
      subscriptionDetails: {
        subscriptionId,
        organizationId,
        userId,
        planId: eventData.payload?.subscription?.entity?.plan_id || 'unknown',
        status: eventData.payload?.subscription?.entity?.status || 'unknown',
        billingInterval: this.mapRazorpayBillingInterval(
          eventData.payload?.subscription?.entity?.plan?.interval
        ),
        currentPeriodStart: new Date(eventData.payload?.subscription?.entity?.current_start * 1000),
        currentPeriodEnd: new Date(eventData.payload?.subscription?.entity?.current_end * 1000),
      },
      paymentProvider: 'razorpay',
      providerEventId: eventData.event_id,
      providerSubscriptionId: eventData.payload?.subscription?.entity?.id,
      providerCustomerId: eventData.payload?.subscription?.entity?.customer_id,
      webhookData: eventData,
      isManual: false,
    };

    // Add payment details if it's a payment event
    if (eventData.payload?.payment?.entity?.amount) {
      logData.paymentDetails = {
        amount: eventData.payload.payment.entity.amount / 100, // Convert from paise
        currency: eventData.payload.payment.entity.currency?.toUpperCase() || 'INR',
        transactionId: eventData.payload.payment.entity.id,
        paymentMethod: eventData.payload.payment.entity.method,
      };
    }

    return await this.createLog(logData);
  }

  static async processPolarWebhook(
    eventData: any,
    subscriptionId: string,
    organizationId: string,
    userId: string
  ): Promise<ISubscriptionLog> {
    const eventType = this.mapPolarEventType(eventData.type);

    const logData: CreateSubscriptionLogInput = {
      eventType,
      subscriptionDetails: {
        subscriptionId,
        organizationId,
        userId,
        planId: eventData.data?.subscription?.product_id || 'unknown',
        status: eventData.data?.subscription?.status || 'unknown',
        billingInterval: this.mapPolarBillingInterval(
          eventData.data?.subscription?.recurring_interval
        ),
        currentPeriodStart: new Date(eventData.data?.subscription?.current_period_start),
        currentPeriodEnd: new Date(eventData.data?.subscription?.current_period_end),
      },
      paymentProvider: 'polar',
      providerEventId: eventData.id,
      providerSubscriptionId: eventData.data?.subscription?.id,
      providerCustomerId: eventData.data?.subscription?.user_id,
      webhookData: eventData,
      isManual: false,
    };

    return await this.createLog(logData);
  }

  // Helper methods for mapping provider-specific event types
  private static mapStripeEventType(stripeEventType: string): SubscriptionEventType {
    const mapping: Record<string, SubscriptionEventType> = {
      'customer.subscription.created': 'subscription_created',
      'customer.subscription.updated': 'subscription_updated',
      'customer.subscription.deleted': 'subscription_cancelled',
      'customer.subscription.paused': 'subscription_paused',
      'customer.subscription.resumed': 'subscription_resumed',
      'invoice.payment_succeeded': 'payment_succeeded',
      'invoice.payment_failed': 'payment_failed',
      'invoice.created': 'invoice_created',
      'invoice.paid': 'invoice_paid',
      'invoice.payment_action_required': 'payment_pending',
      'customer.subscription.trial_will_end': 'trial_ended',
    };

    return mapping[stripeEventType] || 'webhook_received';
  }

  private static mapRazorpayEventType(razorpayEventType: string): SubscriptionEventType {
    const mapping: Record<string, SubscriptionEventType> = {
      'subscription.created': 'subscription_created',
      'subscription.updated': 'subscription_updated',
      'subscription.cancelled': 'subscription_cancelled',
      'subscription.paused': 'subscription_paused',
      'subscription.resumed': 'subscription_resumed',
      'payment.captured': 'payment_succeeded',
      'payment.failed': 'payment_failed',
      'invoice.paid': 'invoice_paid',
    };

    return mapping[razorpayEventType] || 'webhook_received';
  }

  private static mapPolarEventType(polarEventType: string): SubscriptionEventType {
    const mapping: Record<string, SubscriptionEventType> = {
      'subscription.created': 'subscription_created',
      'subscription.updated': 'subscription_updated',
      'subscription.cancelled': 'subscription_cancelled',
      'subscription.active': 'subscription_reactivated',
      'payment.succeeded': 'payment_succeeded',
      'payment.failed': 'payment_failed',
    };

    return mapping[polarEventType] || 'webhook_received';
  }

  private static mapStripeBillingInterval(
    interval?: string
  ): 'monthly' | 'quarterly' | 'yearly' | 'lifetime' {
    const mapping: Record<string, 'monthly' | 'quarterly' | 'yearly' | 'lifetime'> = {
      month: 'monthly',
      year: 'yearly',
      quarter: 'quarterly',
    };

    return mapping[interval || 'month'] || 'monthly';
  }

  private static mapRazorpayBillingInterval(
    interval?: string
  ): 'monthly' | 'quarterly' | 'yearly' | 'lifetime' {
    const mapping: Record<string, 'monthly' | 'quarterly' | 'yearly' | 'lifetime'> = {
      monthly: 'monthly',
      yearly: 'yearly',
      quarterly: 'quarterly',
    };

    return mapping[interval || 'monthly'] || 'monthly';
  }

  private static mapPolarBillingInterval(
    interval?: string
  ): 'monthly' | 'quarterly' | 'yearly' | 'lifetime' {
    const mapping: Record<string, 'monthly' | 'quarterly' | 'yearly' | 'lifetime'> = {
      month: 'monthly',
      year: 'yearly',
      quarter: 'quarterly',
    };

    return mapping[interval || 'month'] || 'monthly';
  }

  // Manual logging methods
  static async logSubscriptionCreated(
    subscriptionId: string,
    organizationId: string,
    userId: string,
    planId: string,
    provider: 'stripe' | 'razorpay' | 'polar',
    processedBy?: string
  ): Promise<ISubscriptionLog> {
    return await this.createLog({
      eventType: 'subscription_created',
      subscriptionDetails: {
        subscriptionId,
        organizationId,
        userId,
        planId,
        status: 'active',
        billingInterval: 'monthly',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      },
      paymentProvider: provider,
      processedBy,
      isManual: true,
    });
  }

  static async logPaymentSucceeded(
    subscriptionId: string,
    organizationId: string,
    userId: string,
    planId: string,
    amount: number,
    currency: string,
    provider: 'stripe' | 'razorpay' | 'polar',
    transactionId?: string,
    processedBy?: string
  ): Promise<ISubscriptionLog> {
    return await this.createLog({
      eventType: 'payment_succeeded',
      subscriptionDetails: {
        subscriptionId,
        organizationId,
        userId,
        planId,
        status: 'active',
        billingInterval: 'monthly',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      },
      paymentDetails: {
        amount,
        currency,
        transactionId,
      },
      paymentProvider: provider,
      processedBy,
      isManual: true,
    });
  }

  static async logPlanUpgrade(
    subscriptionId: string,
    organizationId: string,
    userId: string,
    newPlanId: string,
    previousPlanId: string,
    provider: 'stripe' | 'razorpay' | 'polar',
    processedBy?: string
  ): Promise<ISubscriptionLog> {
    return await this.createLog({
      eventType: 'plan_upgraded',
      subscriptionDetails: {
        subscriptionId,
        organizationId,
        userId,
        planId: newPlanId,
        previousPlanId,
        status: 'active',
        billingInterval: 'monthly',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      },
      paymentProvider: provider,
      processedBy,
      isManual: true,
    });
  }

  // Analytics and reporting
  static async getDashboardStats(organizationId?: string): Promise<any> {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const [revenueStats, subscriptionStats, recentLogs] = await Promise.all([
      this.getRevenueStats(thirtyDaysAgo, now, organizationId),
      this.getSubscriptionStats(organizationId),
      organizationId
        ? SubscriptionLog.findByOrganization(organizationId).then((logs) => logs.slice(0, 10))
        : SubscriptionLog.find({}).sort({ createdAt: -1 }).limit(10),
    ]);

    return {
      revenue: revenueStats,
      subscriptions: subscriptionStats,
      recentActivity: recentLogs,
    };
  }
}
