import mongoose, { Document, Model, Schema } from 'mongoose';
import { nanoid } from 'nanoid';
import { z } from 'zod';

// Zod validation schemas
export const MemberRoleSchema = z.enum([
  'owner',
  'admin',
  'manager',
  'recruiter',
  'interviewer',
  'member',
]);
export const MemberStatusSchema = z.enum(['active', 'inactive', 'pending', 'suspended']);
export const InvitationStatusSchema = z.enum(['pending', 'accepted', 'declined', 'expired']);

export const PermissionsSchema = z.object({
  canManageMembers: z.boolean().default(false),
  canManageJobs: z.boolean().default(false),
  canManageInterviews: z.boolean().default(false),
  canViewAnalytics: z.boolean().default(false),
  canManageSettings: z.boolean().default(false),
  canManageBilling: z.boolean().default(false),
  canDeleteOrganization: z.boolean().default(false),
  canInviteMembers: z.boolean().default(false),
  canRemoveMembers: z.boolean().default(false),
  canCreateJobs: z.boolean().default(false),
  canEditJobs: z.boolean().default(false),
  canDeleteJobs: z.boolean().default(false),
  canConductInterviews: z.boolean().default(false),
  canViewApplications: z.boolean().default(false),
  canManageApplications: z.boolean().default(false),
});

export const CreateOrganizationMemberSchema = z.object({
  organizationId: z.string().min(1, 'Organization ID is required'),
  userId: z.string().min(1, 'User ID is required'),
  role: MemberRoleSchema,
  permissions: PermissionsSchema.optional(),
  invitedBy: z.string().optional(),
  invitationMessage: z.string().optional(),
});

export const UpdateOrganizationMemberSchema = z.object({
  role: MemberRoleSchema.optional(),
  permissions: PermissionsSchema.optional(),
  status: MemberStatusSchema.optional(),
});

export const InviteMemberSchema = z.object({
  organizationId: z.string().min(1, 'Organization ID is required'),
  email: z.string().email('Invalid email format'),
  role: MemberRoleSchema,
  permissions: PermissionsSchema.optional(),
  invitationMessage: z.string().optional(),
  invitedBy: z.string().min(1, 'Inviter ID is required'),
});

// TypeScript interfaces
export type MemberRole = z.infer<typeof MemberRoleSchema>;
export type MemberStatus = z.infer<typeof MemberStatusSchema>;
export type InvitationStatus = z.infer<typeof InvitationStatusSchema>;
export type Permissions = z.infer<typeof PermissionsSchema>;
export type CreateOrganizationMemberInput = z.infer<typeof CreateOrganizationMemberSchema>;
export type UpdateOrganizationMemberInput = z.infer<typeof UpdateOrganizationMemberSchema>;
export type InviteMemberInput = z.infer<typeof InviteMemberSchema>;

export interface IOrganizationMember extends Document {
  _id: mongoose.Types.ObjectId;
  memberId: string;
  organizationId: string;
  userId: string;
  role: MemberRole;
  permissions: Permissions;
  status: MemberStatus;
  invitationStatus: InvitationStatus;
  invitedBy?: string;
  invitedAt?: Date;
  joinedAt?: Date;
  invitationToken?: string;
  invitationExpires?: Date;
  invitationMessage?: string;
  lastActiveAt?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  hasPermission(permission: keyof Permissions): boolean;
  canManage(targetRole: MemberRole): boolean;
  generateInvitationToken(): string;
  acceptInvitation(): Promise<void>;
  declineInvitation(): Promise<void>;
  updateLastActive(): Promise<void>;
  getDefaultPermissions(): Permissions;
}

// Static methods interface
export interface IOrganizationMemberModel extends Model<IOrganizationMember> {
  createMember(memberData: CreateOrganizationMemberInput): Promise<IOrganizationMember>;
  inviteMember(inviteData: InviteMemberInput): Promise<IOrganizationMember>;
  findByOrganization(organizationId: string): Promise<IOrganizationMember[]>;
  findByUser(userId: string): Promise<IOrganizationMember[]>;
  findMember(organizationId: string, userId: string): Promise<IOrganizationMember | null>;
  findByInvitationToken(token: string): Promise<IOrganizationMember | null>;
  getOrganizationOwner(organizationId: string): Promise<IOrganizationMember | null>;
  getMembersByRole(organizationId: string, role: MemberRole): Promise<IOrganizationMember[]>;
  removeMember(organizationId: string, userId: string): Promise<boolean>;
}

// Default permissions by role
const DEFAULT_PERMISSIONS: Record<MemberRole, Permissions> = {
  owner: {
    canManageMembers: true,
    canManageJobs: true,
    canManageInterviews: true,
    canViewAnalytics: true,
    canManageSettings: true,
    canManageBilling: true,
    canDeleteOrganization: true,
    canInviteMembers: true,
    canRemoveMembers: true,
    canCreateJobs: true,
    canEditJobs: true,
    canDeleteJobs: true,
    canConductInterviews: true,
    canViewApplications: true,
    canManageApplications: true,
  },
  admin: {
    canManageMembers: true,
    canManageJobs: true,
    canManageInterviews: true,
    canViewAnalytics: true,
    canManageSettings: true,
    canManageBilling: false,
    canDeleteOrganization: false,
    canInviteMembers: true,
    canRemoveMembers: true,
    canCreateJobs: true,
    canEditJobs: true,
    canDeleteJobs: true,
    canConductInterviews: true,
    canViewApplications: true,
    canManageApplications: true,
  },
  manager: {
    canManageMembers: false,
    canManageJobs: true,
    canManageInterviews: true,
    canViewAnalytics: true,
    canManageSettings: false,
    canManageBilling: false,
    canDeleteOrganization: false,
    canInviteMembers: true,
    canRemoveMembers: false,
    canCreateJobs: true,
    canEditJobs: true,
    canDeleteJobs: false,
    canConductInterviews: true,
    canViewApplications: true,
    canManageApplications: true,
  },
  recruiter: {
    canManageMembers: false,
    canManageJobs: true,
    canManageInterviews: false,
    canViewAnalytics: false,
    canManageSettings: false,
    canManageBilling: false,
    canDeleteOrganization: false,
    canInviteMembers: false,
    canRemoveMembers: false,
    canCreateJobs: true,
    canEditJobs: true,
    canDeleteJobs: false,
    canConductInterviews: false,
    canViewApplications: true,
    canManageApplications: true,
  },
  interviewer: {
    canManageMembers: false,
    canManageJobs: false,
    canManageInterviews: false,
    canViewAnalytics: false,
    canManageSettings: false,
    canManageBilling: false,
    canDeleteOrganization: false,
    canInviteMembers: false,
    canRemoveMembers: false,
    canCreateJobs: false,
    canEditJobs: false,
    canDeleteJobs: false,
    canConductInterviews: true,
    canViewApplications: true,
    canManageApplications: false,
  },
  member: {
    canManageMembers: false,
    canManageJobs: false,
    canManageInterviews: false,
    canViewAnalytics: false,
    canManageSettings: false,
    canManageBilling: false,
    canDeleteOrganization: false,
    canInviteMembers: false,
    canRemoveMembers: false,
    canCreateJobs: false,
    canEditJobs: false,
    canDeleteJobs: false,
    canConductInterviews: false,
    canViewApplications: false,
    canManageApplications: false,
  },
};

// Mongoose schema
const organizationMemberSchema = new Schema<IOrganizationMember>(
  {
    memberId: {
      type: String,
      unique: true,
      default: () => `member_${nanoid(12)}`,
      index: true,
    },
    organizationId: {
      type: String,
      required: true,
      index: true,
    },
    userId: {
      type: String,
      required: true,
      index: true,
    },
    role: {
      type: String,
      enum: ['owner', 'admin', 'manager', 'recruiter', 'interviewer', 'member'],
      required: true,
      index: true,
    },
    permissions: {
      canManageMembers: { type: Boolean, default: false },
      canManageJobs: { type: Boolean, default: false },
      canManageInterviews: { type: Boolean, default: false },
      canViewAnalytics: { type: Boolean, default: false },
      canManageSettings: { type: Boolean, default: false },
      canManageBilling: { type: Boolean, default: false },
      canDeleteOrganization: { type: Boolean, default: false },
      canInviteMembers: { type: Boolean, default: false },
      canRemoveMembers: { type: Boolean, default: false },
      canCreateJobs: { type: Boolean, default: false },
      canEditJobs: { type: Boolean, default: false },
      canDeleteJobs: { type: Boolean, default: false },
      canConductInterviews: { type: Boolean, default: false },
      canViewApplications: { type: Boolean, default: false },
      canManageApplications: { type: Boolean, default: false },
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'pending', 'suspended'],
      default: 'pending',
      index: true,
    },
    invitationStatus: {
      type: String,
      enum: ['pending', 'accepted', 'declined', 'expired'],
      default: 'pending',
      index: true,
    },
    invitedBy: {
      type: String,
      index: true,
    },
    invitedAt: {
      type: Date,
      default: Date.now,
    },
    joinedAt: {
      type: Date,
    },
    invitationToken: {
      type: String,
      index: true,
    },
    invitationExpires: {
      type: Date,
      index: true,
    },
    invitationMessage: {
      type: String,
    },
    lastActiveAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
  }
);

// Compound indexes
organizationMemberSchema.index({ organizationId: 1, userId: 1 }, { unique: true });
organizationMemberSchema.index({ organizationId: 1, role: 1 });
organizationMemberSchema.index({ organizationId: 1, status: 1 });

// Pre-save middleware to set default permissions
organizationMemberSchema.pre('save', function (next) {
  if (this.isNew || this.isModified('role')) {
    const defaultPerms = DEFAULT_PERMISSIONS[this.role];
    this.permissions = { ...defaultPerms, ...this.permissions };
  }
  next();
});

// Instance methods
organizationMemberSchema.methods.hasPermission = function (permission: keyof Permissions): boolean {
  return this.permissions[permission] === true;
};

organizationMemberSchema.methods.canManage = function (targetRole: MemberRole): boolean {
  const roleHierarchy: Record<MemberRole, number> = {
    owner: 6,
    admin: 5,
    manager: 4,
    recruiter: 3,
    interviewer: 2,
    member: 1,
  };

  return roleHierarchy[this.role as MemberRole] > roleHierarchy[targetRole as MemberRole];
};

organizationMemberSchema.methods.generateInvitationToken = function (): string {
  const token = nanoid(32);
  this.invitationToken = token;
  this.invitationExpires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
  return token;
};

organizationMemberSchema.methods.acceptInvitation = async function (): Promise<void> {
  this.invitationStatus = 'accepted';
  this.status = 'active';
  this.joinedAt = new Date();
  this.invitationToken = undefined;
  this.invitationExpires = undefined;
  await this.save();
};

organizationMemberSchema.methods.declineInvitation = async function (): Promise<void> {
  this.invitationStatus = 'declined';
  this.invitationToken = undefined;
  this.invitationExpires = undefined;
  await this.save();
};

organizationMemberSchema.methods.updateLastActive = async function (): Promise<void> {
  this.lastActiveAt = new Date();
  await this.save();
};

organizationMemberSchema.methods.getDefaultPermissions = function (): Permissions {
  return DEFAULT_PERMISSIONS[this.role as MemberRole];
};

// Static methods
organizationMemberSchema.statics.createMember = async function (
  memberData: CreateOrganizationMemberInput
): Promise<IOrganizationMember> {
  // Validate input with Zod
  const validatedData = CreateOrganizationMemberSchema.parse(memberData);

  // Check if member already exists
  const existingMember = await this.findOne({
    organizationId: validatedData.organizationId,
    userId: validatedData.userId,
  });

  if (existingMember) {
    throw new Error('User is already a member of this organization');
  }

  // Create new member
  const member = new this({
    ...validatedData,
    status: 'active',
    invitationStatus: 'accepted',
    joinedAt: new Date(),
  });

  await member.save();
  return member;
};

organizationMemberSchema.statics.inviteMember = async function (
  inviteData: InviteMemberInput
): Promise<IOrganizationMember> {
  // Validate input with Zod
  const validatedData = InviteMemberSchema.parse(inviteData);

  // Create invitation (userId will be set when user accepts)
  const member = new this({
    organizationId: validatedData.organizationId,
    userId: `pending_${nanoid(8)}`, // Temporary ID until user accepts
    role: validatedData.role,
    permissions: validatedData.permissions,
    invitedBy: validatedData.invitedBy,
    invitationMessage: validatedData.invitationMessage,
    status: 'pending',
    invitationStatus: 'pending',
  });

  member.generateInvitationToken();
  await member.save();

  return member;
};

organizationMemberSchema.statics.findByOrganization = async function (
  organizationId: string
): Promise<IOrganizationMember[]> {
  return this.find({ organizationId }).sort({ role: 1, joinedAt: 1 });
};

organizationMemberSchema.statics.findByUser = async function (
  userId: string
): Promise<IOrganizationMember[]> {
  return this.find({ userId, status: 'active' }).sort({ joinedAt: -1 });
};

organizationMemberSchema.statics.findMember = async function (
  organizationId: string,
  userId: string
): Promise<IOrganizationMember | null> {
  return this.findOne({ organizationId, userId });
};

organizationMemberSchema.statics.findByInvitationToken = async function (
  token: string
): Promise<IOrganizationMember | null> {
  return this.findOne({
    invitationToken: token,
    invitationExpires: { $gt: new Date() },
    invitationStatus: 'pending',
  });
};

organizationMemberSchema.statics.getOrganizationOwner = async function (
  organizationId: string
): Promise<IOrganizationMember | null> {
  return this.findOne({ organizationId, role: 'owner', status: 'active' });
};

organizationMemberSchema.statics.getMembersByRole = async function (
  organizationId: string,
  role: MemberRole
): Promise<IOrganizationMember[]> {
  return this.find({ organizationId, role, status: 'active' }).sort({ joinedAt: 1 });
};

organizationMemberSchema.statics.removeMember = async function (
  organizationId: string,
  userId: string
): Promise<boolean> {
  const result = await this.deleteOne({ organizationId, userId });
  return result.deletedCount === 1;
};

// Create and export the model
export const OrganizationMember =
  (mongoose.models.OrganizationMember as IOrganizationMemberModel) ||
  mongoose.model<IOrganizationMember, IOrganizationMemberModel>(
    'OrganizationMember',
    organizationMemberSchema
  );

// Helper functions for Next.js API routes
export class OrganizationMemberService {
  static async addMember(memberData: CreateOrganizationMemberInput): Promise<IOrganizationMember> {
    try {
      return await OrganizationMember.createMember(memberData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  static async inviteMember(inviteData: InviteMemberInput): Promise<IOrganizationMember> {
    try {
      return await OrganizationMember.inviteMember(inviteData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  static async acceptInvitation(
    token: string,
    userId: string
  ): Promise<IOrganizationMember | null> {
    const member = await OrganizationMember.findByInvitationToken(token);
    if (!member) {
      return null;
    }

    // Update the member with the actual user ID
    member.userId = userId;
    await member.acceptInvitation();

    return member;
  }

  static async declineInvitation(token: string): Promise<IOrganizationMember | null> {
    const member = await OrganizationMember.findByInvitationToken(token);
    if (!member) {
      return null;
    }

    await member.declineInvitation();
    return member;
  }

  static async getOrganizationMembers(organizationId: string): Promise<IOrganizationMember[]> {
    return await OrganizationMember.findByOrganization(organizationId);
  }

  static async getUserMemberships(userId: string): Promise<IOrganizationMember[]> {
    return await OrganizationMember.findByUser(userId);
  }

  static async getMember(
    organizationId: string,
    userId: string
  ): Promise<IOrganizationMember | null> {
    return await OrganizationMember.findMember(organizationId, userId);
  }

  static async updateMember(
    organizationId: string,
    userId: string,
    updateData: UpdateOrganizationMemberInput
  ): Promise<IOrganizationMember | null> {
    const validatedData = UpdateOrganizationMemberSchema.parse(updateData);
    return await OrganizationMember.findOneAndUpdate(
      { organizationId, userId },
      { $set: validatedData },
      { new: true, runValidators: true }
    );
  }

  static async removeMember(organizationId: string, userId: string): Promise<boolean> {
    return await OrganizationMember.removeMember(organizationId, userId);
  }

  static async getMembersByRole(
    organizationId: string,
    role: MemberRole
  ): Promise<IOrganizationMember[]> {
    return await OrganizationMember.getMembersByRole(organizationId, role);
  }

  static async getOrganizationOwner(organizationId: string): Promise<IOrganizationMember | null> {
    return await OrganizationMember.getOrganizationOwner(organizationId);
  }

  static async updateLastActive(organizationId: string, userId: string): Promise<void> {
    const member = await OrganizationMember.findMember(organizationId, userId);
    if (member) {
      await member.updateLastActive();
    }
  }

  static async checkPermission(
    organizationId: string,
    userId: string,
    permission: keyof Permissions
  ): Promise<boolean> {
    const member = await OrganizationMember.findMember(organizationId, userId);
    return member ? member.hasPermission(permission) : false;
  }

  static async canManageRole(
    organizationId: string,
    managerId: string,
    targetRole: MemberRole
  ): Promise<boolean> {
    const manager = await OrganizationMember.findMember(organizationId, managerId);
    return manager ? manager.canManage(targetRole) : false;
  }

  static async transferOwnership(
    organizationId: string,
    currentOwnerId: string,
    newOwnerId: string
  ): Promise<{ success: boolean; message: string }> {
    const currentOwner = await OrganizationMember.findMember(organizationId, currentOwnerId);
    const newOwner = await OrganizationMember.findMember(organizationId, newOwnerId);

    if (!currentOwner || currentOwner.role !== 'owner') {
      return { success: false, message: 'Current user is not the owner' };
    }

    if (!newOwner || newOwner.status !== 'active') {
      return { success: false, message: 'New owner is not an active member' };
    }

    // Update roles
    currentOwner.role = 'admin';
    newOwner.role = 'owner';

    await Promise.all([currentOwner.save(), newOwner.save()]);

    return { success: true, message: 'Ownership transferred successfully' };
  }
}
