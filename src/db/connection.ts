import mongoose from 'mongoose';

import { DatabaseSetup } from './setup';

/**
 * Database connection management for Next.js
 * Handles connection pooling, reconnection, and error handling
 */

interface ConnectionOptions {
  maxPoolSize?: number;
  serverSelectionTimeoutMS?: number;
  socketTimeoutMS?: number;
  bufferCommands?: boolean;
  bufferMaxEntries?: number;
}

class DatabaseConnection {
  private static instance: DatabaseConnection;
  private isConnected: boolean = false;
  private connectionPromise: Promise<typeof mongoose> | null = null;

  private constructor() {}

  static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  /**
   * Connect to MongoDB with proper error handling and connection pooling
   */
  async connect(uri?: string, options?: ConnectionOptions): Promise<typeof mongoose> {
    // Return existing connection if already connected
    if (this.isConnected && mongoose.connection.readyState === 1) {
      return mongoose;
    }

    // Return existing connection promise if connection is in progress
    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    const mongoUri = uri || process.env.MONGODB_URI || process.env.DATABASE_URL;

    if (!mongoUri) {
      throw new Error(
        'MongoDB URI is required. Set MONGODB_URI or DATABASE_URL environment variable.'
      );
    }

    const defaultOptions: ConnectionOptions = {
      maxPoolSize: 10, // Maintain up to 10 socket connections
      serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
      bufferCommands: false, // Disable mongoose buffering
      bufferMaxEntries: 0, // Disable mongoose buffering
      ...options,
    };

    console.log('🔌 Connecting to MongoDB...');

    this.connectionPromise = mongoose.connect(mongoUri, defaultOptions);

    try {
      const connection = await this.connectionPromise;
      this.isConnected = true;

      console.log('✅ MongoDB connected successfully');
      console.log(`📊 Database: ${connection.connection.name}`);
      console.log(`🌐 Host: ${connection.connection.host}:${connection.connection.port}`);

      // Set up event listeners
      this.setupEventListeners();

      // Initialize database indexes in development
      if (process.env.NODE_ENV === 'development') {
        await DatabaseSetup.initializeDevelopment();
      } else {
        // In production, just create indexes
        await DatabaseSetup.createIndexes();
      }

      return connection;
    } catch (error) {
      this.isConnected = false;
      this.connectionPromise = null;
      console.error('❌ MongoDB connection failed:', error);
      throw error;
    }
  }

  /**
   * Disconnect from MongoDB
   */
  async disconnect(): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      await mongoose.disconnect();
      this.isConnected = false;
      this.connectionPromise = null;
      console.log('🔌 MongoDB disconnected');
    } catch (error) {
      console.error('❌ Error disconnecting from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): {
    isConnected: boolean;
    readyState: number;
    host?: string;
    name?: string;
  } {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      name: mongoose.connection.name,
    };
  }

  /**
   * Setup event listeners for connection monitoring
   */
  private setupEventListeners(): void {
    mongoose.connection.on('connected', () => {
      console.log('🟢 Mongoose connected to MongoDB');
      this.isConnected = true;
    });

    mongoose.connection.on('error', (error) => {
      console.error('🔴 Mongoose connection error:', error);
      this.isConnected = false;
    });

    mongoose.connection.on('disconnected', () => {
      console.log('🟡 Mongoose disconnected from MongoDB');
      this.isConnected = false;
    });

    mongoose.connection.on('reconnected', () => {
      console.log('🟢 Mongoose reconnected to MongoDB');
      this.isConnected = true;
    });

    // Handle application termination
    process.on('SIGINT', async () => {
      console.log('🛑 Application terminating, closing MongoDB connection...');
      await this.disconnect();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('🛑 Application terminating, closing MongoDB connection...');
      await this.disconnect();
      process.exit(0);
    });
  }

  /**
   * Health check for the database connection
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: any;
  }> {
    try {
      if (!this.isConnected || mongoose.connection.readyState !== 1) {
        return {
          status: 'unhealthy',
          details: {
            readyState: mongoose.connection.readyState,
            error: 'Not connected to database',
          },
        };
      }

      // Test database operation
      if (!mongoose.connection.db) {
        throw new Error('Database connection is not established.');
      }
      await mongoose.connection.db.admin().ping();

      const stats = await DatabaseSetup.getDatabaseStats();

      return {
        status: 'healthy',
        details: {
          readyState: mongoose.connection.readyState,
          host: mongoose.connection.host,
          name: mongoose.connection.name,
          collections: stats.database.collections,
          documents: stats.database.documents,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          readyState: mongoose.connection.readyState,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }
}

// Export singleton instance
export const dbConnection = DatabaseConnection.getInstance();

// Convenience function for Next.js API routes
export async function connectToDatabase(): Promise<typeof mongoose> {
  return dbConnection.connect();
}

// Convenience function for health checks
export async function checkDatabaseHealth() {
  return dbConnection.healthCheck();
}

// Export connection status
export function getDatabaseStatus() {
  return dbConnection.getConnectionStatus();
}

// For Next.js middleware or API routes that need to ensure connection
export async function ensureDbConnection(): Promise<void> {
  const status = dbConnection.getConnectionStatus();

  if (!status.isConnected || status.readyState !== 1) {
    await dbConnection.connect();
  }
}

// Export the connection instance for advanced usage
export { DatabaseConnection };

// Default export for convenience
export default dbConnection;
