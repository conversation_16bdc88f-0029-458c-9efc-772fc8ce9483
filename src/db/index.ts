/**
 * Database Module - Complete Interview Application Database Layer
 *
 * This module provides a comprehensive database layer for the interview application
 * with support for user management, organizations, jobs, applications, AI interviews,
 * and subscription management.
 *
 * Features:
 * - 11 comprehensive schemas with full TypeScript support
 * - Zod validation for all inputs
 * - Helper service classes for Next.js API routes
 * - Advanced search and filtering capabilities
 * - Multi-payment provider support (Stripe, Razorpay, Polar)
 * - AI-powered interview analysis
 * - Comprehensive activity logging
 * - Subscription and billing management
 * - Database connection management with pooling
 * - Automated index creation and maintenance
 *
 * <AUTHOR> App Team
 * @version 1.0.0
 */

// Export models only (types are in @types folder)
export {
  Account,
  ActivityLog,
  AiInterview,
  Job,
  JobApplication,
  Organization,
  OrganizationMember,
  Otp,
  SubscriptionLog,
  SubscriptionPlan,
  User,
} from './models';

// Export all types from @types folder
export * from '../@types';

// Export helpers
export * from '../helpers';

// Connection and setup utilities
import { checkDatabaseHealth, connectToDatabase } from './connection';
import { DatabaseSetup } from './setup';

export {
  checkDatabaseHealth,
  connectToDatabase,
  DatabaseConnection,
  dbConnection,
  ensureDbConnection,
  getDatabaseStatus,
} from './connection';
export { DatabaseSetup, dbUtils } from './setup';

/**
 * Database initialization function for Next.js applications
 * Call this in your app startup or API route middleware
 */
export async function initializeDatabase(options?: {
  uri?: string;
  createIndexes?: boolean;
  validateModels?: boolean;
  cleanupExpired?: boolean;
}): Promise<void> {
  const {
    uri: _uri, // Prefix with underscore to indicate intentionally unused
    createIndexes = true,
    validateModels = true,
    cleanupExpired = false,
  } = options || {};

  try {
    // Connect to database (uri is handled in connectToDatabase if needed)
    await connectToDatabase();

    // Create indexes if requested
    if (createIndexes) {
      await DatabaseSetup.createIndexes();
    }

    // Validate models if requested
    if (validateModels) {
      await DatabaseSetup.validateDatabase();
    }

    // Cleanup expired data if requested
    if (cleanupExpired) {
      await DatabaseSetup.cleanupExpiredData();
    }

    console.log('✅ Database initialization completed successfully');
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
}

/**
 * Quick setup function for development
 */
export async function setupDevelopmentDatabase(): Promise<void> {
  await DatabaseSetup.initializeDevelopment();
}

/**
 * Health check function for monitoring
 */
export async function getDatabaseHealth() {
  return checkDatabaseHealth();
}

/**
 * Get comprehensive database statistics
 */
export async function getDatabaseStatistics() {
  return DatabaseSetup.getDatabaseStats();
}

/**
 * Cleanup function for maintenance
 */
export async function cleanupDatabase() {
  return DatabaseSetup.cleanupExpiredData();
}

// Version information
export const DATABASE_VERSION = '1.0.0';
export const SUPPORTED_MONGODB_VERSIONS = ['4.4', '5.0', '6.0', '7.0'];

// Schema information
export const SCHEMA_INFO = {
  totalSchemas: 11,
  schemas: [
    'User',
    'OTP',
    'Organization',
    'OrganizationMember',
    'Account',
    'ActivityLog',
    'Job',
    'JobApplication',
    'AiInterview',
    'SubscriptionPlan',
    'SubscriptionLog',
  ],
  features: [
    'TypeScript support',
    'Zod validation',
    'Service classes',
    'Advanced indexing',
    'Multi-payment providers',
    'AI interview analysis',
    'Activity tracking',
    'Subscription management',
  ],
};
export * from './mongoose';
