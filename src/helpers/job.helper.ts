import { z } from 'zod';

import type { <PERSON><PERSON><PERSON><PERSON>ience<PERSON><PERSON><PERSON>, JobStatus, JobType, WorkLocation } from '../@types';
import { type CreateJobInput, type IJob, Job, type UpdateJobInput } from '../db/models/job.model';

/**
 * Job Service Helper for Next.js API routes
 * Provides easy-to-use methods for job management operations
 */
export class JobService {
  /**
   * Create a new job
   */
  static async createJob(jobData: CreateJobInput): Promise<IJob> {
    try {
      const job = await Job.createJob(jobData);
      return job as IJob;
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  /**
   * Get job by ID
   */
  static async getJobById(jobId: string): Promise<IJob | null> {
    const job = await Job.findByJobId(jobId);
    return job as <PERSON><PERSON><PERSON> | null;
  }

  /**
   * Update job
   */
  static async updateJob(jobId: string, updateData: UpdateJobInput): Promise<IJob | null> {
    return await Job.findOneAndUpdate(
      { jobId },
      { $set: updateData },
      { new: true, runValidators: true }
    );
  }

  /**
   * Delete job
   */
  static async deleteJob(jobId: string): Promise<boolean> {
    const result = await Job.deleteOne({ jobId });
    return result.deletedCount === 1;
  }

  /**
   * Get jobs by organization
   */
  static async getJobsByOrganization(organizationId: string): Promise<IJob[]> {
    const jobs = await Job.findByOrganization(organizationId);
    return jobs as IJob[];
  }

  /**
   * Get active jobs
   */
  static async getActiveJobs(): Promise<IJob[]> {
    const jobs = await Job.findPublishedJobs({ status: 'published' });
    return jobs as IJob[];
  }

  /**
   * Get jobs by status
   */
  static async getJobsByStatus(status: JobStatus): Promise<IJob[]> {
    const jobs = await Job.find({ status }).sort({ createdAt: -1 });
    return jobs as IJob[];
  }

  /**
   * Search jobs
   */
  static async searchJobs(query: string): Promise<IJob[]> {
    const jobs = await Job.searchJobs(query);
    return jobs as IJob[];
  }

  /**
   * Get jobs by location
   */
  static async getJobsByLocation(location: string): Promise<IJob[]> {
    const jobs = await Job.findByLocation({ country: location });
    return jobs as IJob[];
  }

  /**
   * Get jobs by type
   */
  static async getJobsByType(type: JobType): Promise<IJob[]> {
    const jobs = await Job.find({ type }).sort({ createdAt: -1 });
    return jobs as IJob[];
  }

  /**
   * Get jobs by experience level
   */
  static async getJobsByExperienceLevel(level: JobExperienceLevel): Promise<IJob[]> {
    const jobs = await Job.find({ experienceLevel: level }).sort({ createdAt: -1 });
    return jobs as IJob[];
  }

  /**
   * Publish job
   */
  static async publishJob(jobId: string): Promise<{ success: boolean; message: string }> {
    const job = await Job.findByJobId(jobId);
    if (!job) {
      return { success: false, message: 'Job not found' };
    }

    await job.publish();
    return { success: true, message: 'Job published successfully' };
  }

  /**
   * Pause job
   */
  static async pauseJob(jobId: string): Promise<{ success: boolean; message: string }> {
    const job = await Job.findByJobId(jobId);
    if (!job) {
      return { success: false, message: 'Job not found' };
    }

    job.status = 'paused';
    await job.save();
    return { success: true, message: 'Job paused successfully' };
  }

  /**
   * Close job
   */
  static async closeJob(jobId: string): Promise<{ success: boolean; message: string }> {
    const job = await Job.findByJobId(jobId);
    if (!job) {
      return { success: false, message: 'Job not found' };
    }

    await job.close();
    return { success: true, message: 'Job closed successfully' };
  }

  /**
   * Archive job
   */
  static async archiveJob(jobId: string): Promise<{ success: boolean; message: string }> {
    const job = await Job.findByJobId(jobId);
    if (!job) {
      return { success: false, message: 'Job not found' };
    }

    await job.archive();
    return { success: true, message: 'Job archived successfully' };
  }

  /**
   * Increment job view count
   */
  static async incrementViewCount(jobId: string): Promise<void> {
    const job = await Job.findByJobId(jobId);
    if (job) {
      await job.incrementViewCount();
    }
  }

  /**
   * Add tag to job
   */
  static async addTag(jobId: string, tag: string): Promise<{ success: boolean; message: string }> {
    const job = await Job.findByJobId(jobId);
    if (!job) {
      return { success: false, message: 'Job not found' };
    }

    if (!job.tags.includes(tag)) {
      job.tags.push(tag);
      await job.save();
    }
    return { success: true, message: 'Tag added successfully' };
  }

  /**
   * Remove tag from job
   */
  static async removeTag(
    jobId: string,
    tag: string
  ): Promise<{ success: boolean; message: string }> {
    const job = await Job.findByJobId(jobId);
    if (!job) {
      return { success: false, message: 'Job not found' };
    }

    job.tags = job.tags.filter((t) => t !== tag);
    await job.save();
    return { success: true, message: 'Tag removed successfully' };
  }

  /**
   * Get job statistics
   */
  static async getJobStats(organizationId?: string): Promise<{
    total: number;
    published: number;
    draft: number;
    paused: number;
    closed: number;
    archived: number;
    byType: Record<JobType, number>;
    byExperienceLevel: Record<JobExperienceLevel, number>;
    byWorkLocation: Record<WorkLocation, number>;
    totalViews: number;
    totalApplications: number;
  }> {
    const filter = organizationId ? { organizationId } : {};

    const [
      total,
      published,
      draft,
      paused,
      closed,
      archived,
      byType,
      byExperienceLevel,
      byWorkLocation,
      viewsAndApplications,
    ] = await Promise.all([
      Job.countDocuments(filter),
      Job.countDocuments({ ...filter, status: 'published' }),
      Job.countDocuments({ ...filter, status: 'draft' }),
      Job.countDocuments({ ...filter, status: 'paused' }),
      Job.countDocuments({ ...filter, status: 'closed' }),
      Job.countDocuments({ ...filter, status: 'archived' }),
      Job.aggregate([
        { $match: filter },
        { $group: { _id: '$type', count: { $sum: 1 } } },
        { $group: { _id: null, types: { $push: { type: '$_id', count: '$count' } } } },
      ]),
      Job.aggregate([
        { $match: filter },
        { $group: { _id: '$experienceLevel', count: { $sum: 1 } } },
        { $group: { _id: null, levels: { $push: { level: '$_id', count: '$count' } } } },
      ]),
      Job.aggregate([
        { $match: filter },
        { $group: { _id: '$workLocation', count: { $sum: 1 } } },
        { $group: { _id: null, locations: { $push: { location: '$_id', count: '$count' } } } },
      ]),
      Job.aggregate([
        { $match: filter },
        {
          $group: {
            _id: null,
            totalViews: { $sum: '$viewCount' },
            totalApplications: { $sum: '$applicationCount' },
          },
        },
      ]),
    ]);

    const typeStats =
      byType[0]?.types.reduce((acc: any, item: any) => {
        acc[item.type] = item.count;
        return acc;
      }, {}) || {};

    const levelStats =
      byExperienceLevel[0]?.levels.reduce((acc: any, item: any) => {
        acc[item.level] = item.count;
        return acc;
      }, {}) || {};

    const locationStats =
      byWorkLocation[0]?.locations.reduce((acc: any, item: any) => {
        acc[item.location] = item.count;
        return acc;
      }, {}) || {};

    const { totalViews = 0, totalApplications = 0 } = viewsAndApplications[0] || {};

    return {
      total,
      published,
      draft,
      paused,
      closed,
      archived,
      byType: typeStats,
      byExperienceLevel: levelStats,
      byWorkLocation: locationStats,
      totalViews,
      totalApplications,
    };
  }

  /**
   * Get expired jobs
   */
  static async getExpiredJobs(): Promise<IJob[]> {
    const jobs = await Job.findExpiredJobs();
    return jobs as IJob[];
  }

  /**
   * Get jobs with pagination and filters
   */
  static async getJobsPaginated(options: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    filters?: any;
  }): Promise<{
    jobs: IJob[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      filters = {},
    } = options;

    const skip = (page - 1) * limit;
    const sortObj: Record<string, 1 | -1> = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const [jobs, total] = await Promise.all([
      Job.find(filters).sort(sortObj).skip(skip).limit(limit),
      Job.countDocuments(filters),
    ]);

    const pages = Math.ceil(total / limit);

    return {
      jobs: jobs as IJob[],
      pagination: {
        page,
        limit,
        total,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Get job analytics
   */
  static async getJobAnalytics(organizationId?: string): Promise<any> {
    return await Job.getJobStats(organizationId);
  }

  /**
   * Bulk update jobs
   */
  static async bulkUpdateJobs(
    jobIds: string[],
    updateData: Partial<UpdateJobInput>
  ): Promise<{ modifiedCount: number }> {
    const result = await Job.updateMany({ jobId: { $in: jobIds } }, { $set: updateData });

    return { modifiedCount: result.modifiedCount };
  }

  /**
   * Get job's public view
   */
  static async getPublicJobView(jobId: string): Promise<Partial<IJob> | null> {
    const job = await Job.findByJobId(jobId);
    return job ? job.getPublicData() : null;
  }

  /**
   * Auto-archive expired jobs
   */
  static async autoArchiveExpiredJobs(): Promise<{ archivedCount: number }> {
    const expiredJobs = await Job.findExpiredJobs();

    const archivePromises = expiredJobs.map((job) => job.archive());
    await Promise.all(archivePromises);

    return { archivedCount: expiredJobs.length };
  }

  /**
   * Export jobs data
   */
  static async exportJobs(filters?: any): Promise<Partial<IJob>[]> {
    const jobs = await Job.find(filters || {});
    return jobs.map((job) => job.toObject()) as Partial<IJob>[];
  }
}
