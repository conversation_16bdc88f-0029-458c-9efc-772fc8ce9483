import { z } from 'zod';

import type {
  ChangePasswordInput,
  CreateUserInput,
  IUser,
  LoginInput,
  UpdateUserInput,
  UserRole,
} from '../@types';
import { User } from '../db/models/user.model';

/**
 * User Service Helper for Next.js API routes
 * Provides easy-to-use methods for user management operations
 */
export class UserService {
  /**
   * Create a new user
   */
  static async createUser(userData: CreateUserInput): Promise<IUser> {
    try {
      return await User.createUser(userData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  /**
   * Get user by ID
   */
  static async getUserById(userId: string): Promise<IUser | null> {
    return await User.findByUserId(userId);
  }

  /**
   * Get user by email
   */
  static async getUserByEmail(email: string): Promise<IUser | null> {
    return await User.findByEmail(email);
  }

  /**
   * Update user
   */
  static async updateUser(userId: string, updateData: UpdateUserInput): Promise<IUser | null> {
    return await User.findOneAndUpdate(
      { userId },
      { $set: updateData },
      { new: true, runValidators: true }
    );
  }

  /**
   * Delete user
   */
  static async deleteUser(userId: string): Promise<boolean> {
    const result = await User.deleteOne({ userId });
    return result.deletedCount === 1;
  }

  /**
   * Authenticate user
   */
  static async authenticateUser(loginData: LoginInput): Promise<{
    user: IUser | null;
    error?: string;
  }> {
    return await User.authenticate(loginData.email, loginData.password);
  }

  /**
   * Change user password
   */
  static async changePassword(
    userId: string,
    passwordData: ChangePasswordInput
  ): Promise<{ success: boolean; message: string }> {
    const user = await User.findByUserId(userId);
    if (!user) {
      return { success: false, message: 'User not found' };
    }

    const isCurrentPasswordValid = await user.comparePassword(passwordData.currentPassword);
    if (!isCurrentPasswordValid) {
      return { success: false, message: 'Current password is incorrect' };
    }

    user.password = passwordData.newPassword;
    await user.save();

    return { success: true, message: 'Password changed successfully' };
  }

  /**
   * Reset password
   */
  static async resetPassword(
    email: string
  ): Promise<{ success: boolean; token?: string; message: string }> {
    const user = await User.findByEmail(email);
    if (!user) {
      return { success: false, message: 'User not found' };
    }

    const resetToken = user.generatePasswordResetToken();
    await user.save();

    return { success: true, token: resetToken, message: 'Password reset token generated' };
  }

  /**
   * Verify email
   */
  static async verifyEmail(userId: string): Promise<{ success: boolean; message: string }> {
    const user = await User.findByUserId(userId);
    if (!user) {
      return { success: false, message: 'User not found' };
    }

    user.isEmailVerified = true;
    user.emailVerifiedAt = new Date();
    await user.save();

    return { success: true, message: 'Email verified successfully' };
  }

  /**
   * Get users by role
   */
  static async getUsersByRole(role: UserRole): Promise<IUser[]> {
    return await User.findByRole(role);
  }

  /**
   * Search users
   */
  static async searchUsers(query: string): Promise<IUser[]> {
    return await User.searchUsers(query);
  }

  /**
   * Get active users
   */
  static async getActiveUsers(): Promise<IUser[]> {
    return await User.findActiveUsers();
  }

  /**
   * Update last active timestamp
   */
  static async updateLastActive(userId: string): Promise<void> {
    const user = await User.findByUserId(userId);
    if (user) {
      await user.updateLastActive();
    }
  }

  /**
   * Enable 2FA for user
   */
  static async enable2FA(
    userId: string
  ): Promise<{ success: boolean; secret?: string; message: string }> {
    const user = await User.findByUserId(userId);
    if (!user) {
      return { success: false, message: 'User not found' };
    }

    const secret = await user.enable2FA();
    return { success: true, secret, message: '2FA enabled successfully' };
  }

  /**
   * Disable 2FA for user
   */
  static async disable2FA(userId: string): Promise<{ success: boolean; message: string }> {
    const user = await User.findByUserId(userId);
    if (!user) {
      return { success: false, message: 'User not found' };
    }

    await user.disable2FA();
    return { success: true, message: '2FA disabled successfully' };
  }

  /**
   * Verify 2FA token
   */
  static async verify2FA(
    userId: string,
    token: string
  ): Promise<{ success: boolean; message: string }> {
    const user = await User.findByUserId(userId);
    if (!user) {
      return { success: false, message: 'User not found' };
    }

    const isValid = user.verify2FA(token);
    return {
      success: isValid,
      message: isValid ? '2FA token verified' : 'Invalid 2FA token',
    };
  }

  /**
   * Get user statistics
   */
  static async getUserStats(): Promise<{
    total: number;
    active: number;
    verified: number;
    byRole: Record<UserRole, number>;
  }> {
    const [total, active, verified, byRole] = await Promise.all([
      User.countDocuments(),
      User.countDocuments({ status: 'active' }),
      User.countDocuments({ isEmailVerified: true }),
      User.aggregate([
        { $group: { _id: '$role', count: { $sum: 1 } } },
        { $group: { _id: null, roles: { $push: { role: '$_id', count: '$count' } } } },
      ]),
    ]);

    const roleStats =
      byRole[0]?.roles.reduce((acc: any, item: any) => {
        acc[item.role] = item.count;
        return acc;
      }, {}) || {};

    return {
      total,
      active,
      verified,
      byRole: roleStats,
    };
  }

  /**
   * Suspend user
   */
  static async suspendUser(
    userId: string,
    reason?: string
  ): Promise<{ success: boolean; message: string }> {
    const result = await User.findOneAndUpdate(
      { userId },
      {
        $set: {
          status: 'suspended',
          suspendedAt: new Date(),
          suspensionReason: reason,
        },
      },
      { new: true }
    );

    return {
      success: !!result,
      message: result ? 'User suspended successfully' : 'User not found',
    };
  }

  /**
   * Reactivate user
   */
  static async reactivateUser(userId: string): Promise<{ success: boolean; message: string }> {
    const result = await User.findOneAndUpdate(
      { userId },
      {
        $set: { status: 'active' },
        $unset: { suspendedAt: 1, suspensionReason: 1 },
      },
      { new: true }
    );

    return {
      success: !!result,
      message: result ? 'User reactivated successfully' : 'User not found',
    };
  }

  /**
   * Get user's public profile
   */
  static async getPublicProfile(userId: string): Promise<Partial<IUser> | null> {
    const user = await User.findByUserId(userId);
    return user ? user.getPublicProfile() : null;
  }

  /**
   * Bulk operations
   */
  static async bulkUpdateUsers(
    userIds: string[],
    updateData: Partial<UpdateUserInput>
  ): Promise<{ modifiedCount: number }> {
    const result = await User.updateMany({ userId: { $in: userIds } }, { $set: updateData });

    return { modifiedCount: result.modifiedCount };
  }

  /**
   * Export users data
   */
  static async exportUsers(filters?: any): Promise<Partial<IUser>[]> {
    const users = await User.find(filters || {}).select(
      '-password -passwordResetToken -twoFactorSecret'
    );
    return users.map((user) => user.toObject());
  }
}
