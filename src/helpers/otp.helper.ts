import { z } from 'zod';

import type { OtpType } from '../@types';
import { type CreateOtpInput, type IOtp, Otp, type VerifyOtpInput } from '../db/models/otp.model';

/**
 * OTP Service Helper for Next.js API routes
 * Provides easy-to-use methods for OTP management operations
 */
export class OtpService {
  /**
   * Create a new OTP
   */
  static async createOtp(otpData: CreateOtpInput): Promise<IOtp> {
    try {
      // Generate 6-digit code
      const code = Math.floor(100000 + Math.random() * 900000).toString();

      // Convert the input to match the model's expected format
      const modelInput = {
        email: otpData.email,
        type: otpData.type,
        code,
        metadata: otpData.metadata,
      };

      return await Otp.createOtp(modelInput);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  /**
   * Verify OTP
   */
  static async verifyOtp(verifyData: VerifyOtpInput): Promise<{
    success: boolean;
    message: string;
    otp?: IOtp;
  }> {
    const result = await Otp.verifyOtp(verifyData);
    return {
      success: result.isValid,
      message: result.message,
      otp: result.otp || undefined,
    };
  }

  /**
   * Get valid OTP for email and type
   */
  static async getValidOtp(email: string, type: OtpType): Promise<IOtp | null> {
    return await Otp.findValidOtp(email, type);
  }

  /**
   * Get OTPs by email
   */
  static async getOtpsByEmail(email: string): Promise<IOtp[]> {
    return await Otp.findByEmail(email);
  }

  /**
   * Resend OTP
   */
  static async resendOtp(
    email: string,
    type: OtpType
  ): Promise<{
    success: boolean;
    otp?: IOtp;
    message: string;
  }> {
    try {
      // Check if there's already a valid OTP
      const existingOtp = await Otp.findValidOtp(email, type);

      if (existingOtp && !existingOtp.isExpired()) {
        const timeRemaining = Math.ceil((existingOtp.expiresAt.getTime() - Date.now()) / 1000 / 60);
        return {
          success: false,
          message: `Please wait ${timeRemaining} minutes before requesting a new OTP`,
        };
      }

      // Create new OTP
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      const newOtp = await Otp.createOtp({ email, type, code });

      return {
        success: true,
        otp: newOtp,
        message: 'New OTP sent successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to resend OTP',
      };
    }
  }

  /**
   * Cleanup expired OTPs
   */
  static async cleanupExpiredOtps(): Promise<{ deletedCount: number }> {
    const deletedCount = await Otp.cleanupExpiredOtps();
    return { deletedCount };
  }

  /**
   * Get OTP statistics
   */
  static async getOtpStats(): Promise<{
    total: number;
    pending: number;
    verified: number;
    expired: number;
    failed: number;
    byType: Record<OtpType, number>;
  }> {
    const [total, pending, verified, expired, failed, byType] = await Promise.all([
      Otp.countDocuments(),
      Otp.countDocuments({ status: 'pending' }),
      Otp.countDocuments({ status: 'verified' }),
      Otp.countDocuments({ status: 'expired' }),
      Otp.countDocuments({ status: 'failed' }),
      Otp.aggregate([
        { $group: { _id: '$type', count: { $sum: 1 } } },
        { $group: { _id: null, types: { $push: { type: '$_id', count: '$count' } } } },
      ]),
    ]);

    const typeStats =
      byType[0]?.types.reduce((acc: any, item: any) => {
        acc[item.type] = item.count;
        return acc;
      }, {}) || {};

    return {
      total,
      pending,
      verified,
      expired,
      failed,
      byType: typeStats,
    };
  }

  /**
   * Invalidate all OTPs for email and type
   */
  static async invalidateOtps(email: string, type?: OtpType): Promise<{ modifiedCount: number }> {
    const filter: any = { email, status: 'pending' };
    if (type) {
      filter.type = type;
    }

    const result = await Otp.updateMany(filter, { $set: { status: 'expired' } });

    return { modifiedCount: result.modifiedCount };
  }

  /**
   * Get OTP by ID
   */
  static async getOtpById(otpId: string): Promise<IOtp | null> {
    return await Otp.findOne({ otpId });
  }

  /**
   * Check rate limiting for OTP requests
   */
  static async checkRateLimit(
    email: string,
    type: OtpType,
    windowMinutes: number = 60,
    maxAttempts: number = 5
  ): Promise<{
    allowed: boolean;
    attemptsRemaining: number;
    resetTime?: Date;
  }> {
    const windowStart = new Date(Date.now() - windowMinutes * 60 * 1000);

    const recentOtps = await Otp.find({
      email,
      type,
      createdAt: { $gte: windowStart },
    }).sort({ createdAt: -1 });

    const attemptsUsed = recentOtps.length;
    const attemptsRemaining = Math.max(0, maxAttempts - attemptsUsed);

    if (attemptsUsed >= maxAttempts) {
      const oldestOtp = recentOtps[recentOtps.length - 1];
      const resetTime = new Date(oldestOtp.createdAt.getTime() + windowMinutes * 60 * 1000);

      return {
        allowed: false,
        attemptsRemaining: 0,
        resetTime,
      };
    }

    return {
      allowed: true,
      attemptsRemaining,
    };
  }

  /**
   * Generate OTP for testing (development only)
   */
  static async generateTestOtp(
    email: string,
    type: OtpType
  ): Promise<{
    success: boolean;
    otp?: IOtp;
    code?: string;
    message: string;
  }> {
    if (process.env.NODE_ENV === 'production') {
      return {
        success: false,
        message: 'Test OTP generation is not allowed in production',
      };
    }

    try {
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      const otp = await Otp.createOtp({ email, type, code });

      return {
        success: true,
        otp,
        code: otp.code,
        message: 'Test OTP generated successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to generate test OTP',
      };
    }
  }

  /**
   * Get OTP analytics
   */
  static async getOtpAnalytics(days: number = 30): Promise<{
    dailyStats: Array<{
      date: string;
      created: number;
      verified: number;
      expired: number;
      failed: number;
    }>;
    typeBreakdown: Record<
      OtpType,
      {
        created: number;
        verified: number;
        successRate: number;
      }
    >;
    averageVerificationTime: number;
  }> {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    // Daily stats
    const dailyStats = await Otp.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
            status: '$status',
          },
          count: { $sum: 1 },
        },
      },
      {
        $group: {
          _id: '$_id.date',
          stats: {
            $push: {
              status: '$_id.status',
              count: '$count',
            },
          },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    // Type breakdown
    const typeBreakdown = await Otp.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: {
            type: '$type',
            status: '$status',
          },
          count: { $sum: 1 },
        },
      },
      {
        $group: {
          _id: '$_id.type',
          stats: {
            $push: {
              status: '$_id.status',
              count: '$count',
            },
          },
        },
      },
    ]);

    // Average verification time
    const verificationTimes = await Otp.aggregate([
      {
        $match: {
          status: 'verified',
          verifiedAt: { $exists: true },
          createdAt: { $gte: startDate },
        },
      },
      {
        $project: {
          verificationTime: {
            $subtract: ['$verifiedAt', '$createdAt'],
          },
        },
      },
      {
        $group: {
          _id: null,
          averageTime: { $avg: '$verificationTime' },
        },
      },
    ]);

    return {
      dailyStats: dailyStats.map((day) => {
        const stats = day.stats.reduce((acc: any, stat: any) => {
          acc[stat.status] = stat.count;
          return acc;
        }, {});

        return {
          date: day._id,
          created: stats.pending || 0,
          verified: stats.verified || 0,
          expired: stats.expired || 0,
          failed: stats.failed || 0,
        };
      }),
      typeBreakdown: typeBreakdown.reduce((acc: any, type: any) => {
        const stats = type.stats.reduce((typeAcc: any, stat: any) => {
          typeAcc[stat.status] = stat.count;
          return typeAcc;
        }, {});

        const created =
          (stats.pending || 0) + (stats.verified || 0) + (stats.expired || 0) + (stats.failed || 0);
        const verified = stats.verified || 0;

        acc[type._id] = {
          created,
          verified,
          successRate: created > 0 ? (verified / created) * 100 : 0,
        };

        return acc;
      }, {}),
      averageVerificationTime: verificationTimes[0]?.averageTime || 0,
    };
  }
}
