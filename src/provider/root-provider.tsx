import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { NuqsAdapter } from 'nuqs/adapters/next/app';

import { AuthProvider } from '@/contexts/AuthContext';
import { TRPCReactProvider } from '@/trpc/react';

import ReactQueryProvider from './_react-query-provider';
import { ThemeProvider } from './_theme-provider';

interface RootProviderProps {
  children: React.ReactNode;
}

export default async function RootProvider({ children }: RootProviderProps) {
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <ReactQueryProvider>
        <TRPCReactProvider>
          <AuthProvider>
            {/* NuqsAdapter is used to provide the Nuqs context for the application */}
            <ThemeProvider
              attribute="class"
              defaultTheme="dark"
              enableSystem
              disableTransitionOnChange
            >
              <NuqsAdapter>{children}</NuqsAdapter>
            </ThemeProvider>
          </AuthProvider>
        </TRPCReactProvider>
      </ReactQueryProvider>
    </NextIntlClientProvider>
  );
}
