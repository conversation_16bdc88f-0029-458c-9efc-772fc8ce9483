import { Document, Model } from 'mongoose';
import { z } from 'zod';

// Zod validation schemas
export const OrganizationTypeSchema = z.enum([
  'startup',
  'small_business',
  'medium_business',
  'enterprise',
  'non_profit',
  'government',
  'other',
]);
export const OrganizationStatusSchema = z.enum([
  'active',
  'inactive',
  'suspended',
  'pending_verification',
]);
export const IndustrySchema = z.enum([
  'technology',
  'healthcare',
  'finance',
  'education',
  'retail',
  'manufacturing',
  'consulting',
  'media',
  'real_estate',
  'transportation',
  'energy',
  'agriculture',
  'construction',
  'hospitality',
  'legal',
  'non_profit',
  'government',
  'other',
]);

export const AddressSchema = z.object({
  street: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().min(1, 'Country is required'),
  postalCode: z.string().optional(),
  timezone: z.string().default('UTC'),
});

export const SocialLinksSchema = z.object({
  website: z.string().url().optional(),
  linkedin: z.string().url().optional(),
  twitter: z.string().url().optional(),
  facebook: z.string().url().optional(),
  instagram: z.string().url().optional(),
  github: z.string().url().optional(),
});

export const OrganizationSettingsSchema = z.object({
  allowPublicJobs: z.boolean().default(true),
  requireApprovalForJobs: z.boolean().default(false),
  autoArchiveJobsAfterDays: z.number().min(1).max(365).default(90),
  allowAnonymousApplications: z.boolean().default(true),
  requireCoverLetter: z.boolean().default(false),
  enableAiScreening: z.boolean().default(false),
  notificationSettings: z
    .object({
      emailNotifications: z.boolean().default(true),
      slackNotifications: z.boolean().default(false),
      webhookUrl: z.string().url().optional(),
    })
    .default({}),
  brandingSettings: z
    .object({
      primaryColor: z.string().default('#3B82F6'),
      secondaryColor: z.string().default('#1F2937'),
      logo: z.string().url().optional(),
      customDomain: z.string().optional(),
    })
    .default({}),
});

export const CreateOrganizationSchema = z.object({
  name: z.string().min(1, 'Organization name is required'),
  slug: z
    .string()
    .min(1, 'Organization slug is required')
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  description: z.string().optional(),
  type: OrganizationTypeSchema,
  industry: IndustrySchema,
  size: z.enum(['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']),
  foundedYear: z.number().min(1800).max(new Date().getFullYear()).optional(),
  address: AddressSchema,
  socialLinks: SocialLinksSchema.optional(),
  logo: z.string().url().optional(),
  settings: OrganizationSettingsSchema.optional(),
  ownerId: z.string().min(1, 'Owner ID is required'),
});

export const UpdateOrganizationSchema = CreateOrganizationSchema.partial().omit({ ownerId: true });

// TypeScript interfaces
export type OrganizationType = z.infer<typeof OrganizationTypeSchema>;
export type OrganizationStatus = z.infer<typeof OrganizationStatusSchema>;
export type Industry = z.infer<typeof IndustrySchema>;
export type Address = z.infer<typeof AddressSchema>;
export type SocialLinks = z.infer<typeof SocialLinksSchema>;
export type OrganizationSettings = z.infer<typeof OrganizationSettingsSchema>;
export type CreateOrganizationInput = z.infer<typeof CreateOrganizationSchema>;
export type UpdateOrganizationInput = z.infer<typeof UpdateOrganizationSchema>;

export interface IOrganization extends Document {
  _id: any;
  organizationId: string;
  name: string;
  slug: string;
  description?: string;
  type: OrganizationType;
  industry: Industry;
  size: string;
  foundedYear?: number;
  address: Address;
  socialLinks?: SocialLinks;
  logo?: string;
  status: OrganizationStatus;
  isVerified: boolean;
  verifiedAt?: Date;
  ownerId: string;
  memberCount: number;
  jobCount: number;
  settings: OrganizationSettings;
  subscriptionPlanId?: string;
  subscriptionStatus: string;
  trialEndsAt?: Date;
  suspendedAt?: Date;
  suspensionReason?: string;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  updateMemberCount(): Promise<number>;
  updateJobCount(): Promise<number>;
  isOwner(userId: string): boolean;
  canCreateJobs(): boolean;
  getPublicProfile(): Partial<IOrganization>;
  generateSlug(): string;
  updateSettings(newSettings: Partial<OrganizationSettings>): Promise<void>;
}

export interface IOrganizationModel extends Model<IOrganization> {
  createOrganization(orgData: CreateOrganizationInput): Promise<IOrganization>;
  findBySlug(slug: string): Promise<IOrganization | null>;
  findByOrganizationId(organizationId: string): Promise<IOrganization | null>;
  findByOwner(ownerId: string): Promise<IOrganization[]>;
  findByIndustry(industry: Industry): Promise<IOrganization[]>;
  findActiveOrganizations(): Promise<IOrganization[]>;
  searchOrganizations(query: string): Promise<IOrganization[]>;
}
