import { Document, Model } from 'mongoose';
import { z } from 'zod';

// Zod validation schemas
export const MemberRoleSchema = z.enum([
  'owner',
  'admin',
  'manager',
  'recruiter',
  'interviewer',
  'member',
]);
export const MemberStatusSchema = z.enum(['active', 'inactive', 'pending', 'suspended']);
export const InvitationStatusSchema = z.enum(['pending', 'accepted', 'declined', 'expired']);

export const PermissionsSchema = z.object({
  canManageMembers: z.boolean().default(false),
  canManageJobs: z.boolean().default(false),
  canManageInterviews: z.boolean().default(false),
  canViewAnalytics: z.boolean().default(false),
  canManageSettings: z.boolean().default(false),
  canManageBilling: z.boolean().default(false),
  canDeleteOrganization: z.boolean().default(false),
  canInviteMembers: z.boolean().default(false),
  canRemoveMembers: z.boolean().default(false),
  canCreateJobs: z.boolean().default(false),
  canEditJobs: z.boolean().default(false),
  canDeleteJobs: z.boolean().default(false),
  canConductInterviews: z.boolean().default(false),
  canViewApplications: z.boolean().default(false),
  canManageApplications: z.boolean().default(false),
});

export const CreateOrganizationMemberSchema = z.object({
  organizationId: z.string().min(1, 'Organization ID is required'),
  userId: z.string().min(1, 'User ID is required'),
  role: MemberRoleSchema,
  permissions: PermissionsSchema.optional(),
  invitedBy: z.string().optional(),
  invitationMessage: z.string().optional(),
});

export const UpdateOrganizationMemberSchema = z.object({
  role: MemberRoleSchema.optional(),
  permissions: PermissionsSchema.optional(),
  status: MemberStatusSchema.optional(),
});

export const InviteMemberSchema = z.object({
  organizationId: z.string().min(1, 'Organization ID is required'),
  email: z.string().email('Invalid email format'),
  role: MemberRoleSchema,
  permissions: PermissionsSchema.optional(),
  invitationMessage: z.string().optional(),
  invitedBy: z.string().min(1, 'Inviter ID is required'),
});

// TypeScript interfaces
export type MemberRole = z.infer<typeof MemberRoleSchema>;
export type MemberStatus = z.infer<typeof MemberStatusSchema>;
export type InvitationStatus = z.infer<typeof InvitationStatusSchema>;
export type Permissions = z.infer<typeof PermissionsSchema>;
export type CreateOrganizationMemberInput = z.infer<typeof CreateOrganizationMemberSchema>;
export type UpdateOrganizationMemberInput = z.infer<typeof UpdateOrganizationMemberSchema>;
export type InviteMemberInput = z.infer<typeof InviteMemberSchema>;

export interface IOrganizationMember extends Document {
  _id: any;
  memberId: string;
  organizationId: string;
  userId: string;
  role: MemberRole;
  permissions: Permissions;
  status: MemberStatus;
  invitationStatus: InvitationStatus;
  invitedBy?: string;
  invitedAt?: Date;
  joinedAt?: Date;
  invitationToken?: string;
  invitationExpires?: Date;
  invitationMessage?: string;
  lastActiveAt?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  hasPermission(permission: keyof Permissions): boolean;
  canManage(targetRole: MemberRole): boolean;
  generateInvitationToken(): string;
  acceptInvitation(): Promise<void>;
  declineInvitation(): Promise<void>;
  updateLastActive(): Promise<void>;
  getDefaultPermissions(): Permissions;
}

export interface IOrganizationMemberModel extends Model<IOrganizationMember> {
  createMember(memberData: CreateOrganizationMemberInput): Promise<IOrganizationMember>;
  inviteMember(inviteData: InviteMemberInput): Promise<IOrganizationMember>;
  findByOrganization(organizationId: string): Promise<IOrganizationMember[]>;
  findByUser(userId: string): Promise<IOrganizationMember[]>;
  findMember(organizationId: string, userId: string): Promise<IOrganizationMember | null>;
  findByInvitationToken(token: string): Promise<IOrganizationMember | null>;
  getOrganizationOwner(organizationId: string): Promise<IOrganizationMember | null>;
  getMembersByRole(organizationId: string, role: MemberRole): Promise<IOrganizationMember[]>;
  removeMember(organizationId: string, userId: string): Promise<boolean>;
}
