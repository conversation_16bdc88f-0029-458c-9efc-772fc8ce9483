import { Document, Model } from 'mongoose';
import { z } from 'zod';

// Zod validation schemas
export const OtpTypeSchema = z.enum([
  'email_verification',
  'password_reset',
  'login_verification',
  'phone_verification',
]);
export const OtpStatusSchema = z.enum(['pending', 'verified', 'expired', 'failed']);

export const CreateOtpSchema = z.object({
  email: z.string().email('Invalid email format'),
  type: OtpTypeSchema,
  expiresInMinutes: z.number().min(1).max(60).default(10),
  metadata: z.record(z.unknown()).optional(),
});

export const VerifyOtpSchema = z.object({
  email: z.string().email('Invalid email format'),
  code: z.string().length(6, 'OTP must be 6 digits'),
  type: OtpTypeSchema,
});

// TypeScript interfaces
export type OtpType = z.infer<typeof OtpTypeSchema>;
export type OtpStatus = z.infer<typeof OtpStatusSchema>;
export type CreateOtpInput = z.infer<typeof CreateOtpSchema>;
export type VerifyOtpInput = z.infer<typeof VerifyOtpSchema>;

export interface IOtp extends Document {
  _id: any;
  otpId: string;
  email: string;
  code: string;
  type: OtpType;
  status: OtpStatus;
  attempts: number;
  maxAttempts: number;
  expiresAt: Date;
  verifiedAt?: Date;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  isExpired(): boolean;
  isValid(): boolean;
  verify(inputCode: string): Promise<boolean>;
  markAsVerified(): Promise<void>;
  incrementAttempts(): Promise<void>;
  generateNewCode(): void;
}

export interface IOtpModel extends Model<IOtp> {
  createOtp(otpData: CreateOtpInput): Promise<IOtp>;
  findValidOtp(email: string, type: OtpType): Promise<IOtp | null>;
  verifyOtp(verifyData: VerifyOtpInput): Promise<{ success: boolean; message: string; otp?: IOtp }>;
  cleanupExpiredOtps(): Promise<number>;
  findByEmail(email: string): Promise<IOtp[]>;
}
