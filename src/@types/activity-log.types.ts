import { Document, Model } from 'mongoose';
import { z } from 'zod';

// Zod validation schemas
export const ActivityTypeSchema = z.enum([
  'user_login',
  'user_logout',
  'user_register',
  'user_update',
  'user_delete',
  'password_change',
  'password_reset',
  'email_verification',
  'profile_update',
  'organization_create',
  'organization_update',
  'organization_delete',
  'member_invite',
  'member_join',
  'member_leave',
  'member_remove',
  'role_change',
  'job_create',
  'job_update',
  'job_delete',
  'job_publish',
  'job_archive',
  'application_submit',
  'application_update',
  'application_withdraw',
  'application_review',
  'interview_schedule',
  'interview_conduct',
  'interview_complete',
  'interview_cancel',
  'ai_interview_start',
  'ai_interview_complete',
  'ai_analysis_generate',
  'subscription_create',
  'subscription_update',
  'subscription_cancel',
  'payment_process',
  'settings_update',
  'data_export',
  'data_import',
  'security_alert',
  'system_maintenance',
  'api_access',
  'webhook_trigger',
  'integration_sync',
]);

export const ActivityLevelSchema = z.enum(['info', 'warning', 'error', 'critical', 'debug']);
export const ActivityStatusSchema = z.enum(['success', 'failure', 'pending', 'cancelled']);

export const CreateActivityLogSchema = z.object({
  type: ActivityTypeSchema,
  level: ActivityLevelSchema.default('info'),
  status: ActivityStatusSchema.default('success'),
  userId: z.string().optional(),
  organizationId: z.string().optional(),
  resourceId: z.string().optional(),
  resourceType: z.string().optional(),
  action: z.string().min(1, 'Action is required'),
  description: z.string().optional(),
  metadata: z.record(z.unknown()).optional(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
  sessionId: z.string().optional(),
  apiKey: z.string().optional(),
  duration: z.number().min(0).optional(),
  errorCode: z.string().optional(),
  errorMessage: z.string().optional(),
  tags: z.array(z.string()).default([]),
});

// TypeScript interfaces
export type ActivityType = z.infer<typeof ActivityTypeSchema>;
export type ActivityLevel = z.infer<typeof ActivityLevelSchema>;
export type ActivityStatus = z.infer<typeof ActivityStatusSchema>;
export type CreateActivityLogInput = z.infer<typeof CreateActivityLogSchema>;

export interface IActivityLog extends Document {
  _id: any;
  logId: string;
  type: ActivityType;
  level: ActivityLevel;
  status: ActivityStatus;
  userId?: string;
  organizationId?: string;
  resourceId?: string;
  resourceType?: string;
  action: string;
  description?: string;
  metadata?: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  apiKey?: string;
  duration?: number;
  errorCode?: string;
  errorMessage?: string;
  tags: string[];
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  isError(): boolean;
  isCritical(): boolean;
  getFormattedTimestamp(): string;
  addTag(tag: string): Promise<void>;
  removeTag(tag: string): Promise<void>;
}

export interface IActivityLogModel extends Model<IActivityLog> {
  createLog(logData: CreateActivityLogInput): Promise<IActivityLog>;
  findByUser(userId: string): Promise<IActivityLog[]>;
  findByOrganization(organizationId: string): Promise<IActivityLog[]>;
  findByType(type: ActivityType): Promise<IActivityLog[]>;
  findByLevel(level: ActivityLevel): Promise<IActivityLog[]>;
  findByDateRange(startDate: Date, endDate: Date): Promise<IActivityLog[]>;
  findErrors(): Promise<IActivityLog[]>;
  findCritical(): Promise<IActivityLog[]>;
  getAnalytics(organizationId?: string): Promise<any>;
  cleanupOldLogs(daysToKeep: number): Promise<number>;
}
