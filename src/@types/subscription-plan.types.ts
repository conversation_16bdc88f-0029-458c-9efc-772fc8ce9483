import { Document, Model } from 'mongoose';
import { z } from 'zod';

// Zod validation schemas
export const PlanTypeSchema = z.enum(['free', 'basic', 'professional', 'enterprise', 'custom']);
export const BillingIntervalSchema = z.enum(['monthly', 'quarterly', 'yearly', 'lifetime']);
export const PlanStatusSchema = z.enum(['active', 'inactive', 'deprecated', 'coming_soon']);
export const PaymentProviderSchema = z.enum(['stripe', 'razorpay', 'polar']);

export const PricingSchema = z.object({
  amount: z.number().min(0),
  currency: z.string().length(3).default('USD'),
  interval: BillingIntervalSchema,
  intervalCount: z.number().min(1).default(1), // e.g., every 3 months
});

export const FeatureLimitSchema = z.object({
  name: z.string(),
  limit: z.number().min(-1), // -1 for unlimited
  description: z.string().optional(),
});

export const PlanFeaturesSchema = z.object({
  // Core features
  maxJobs: z.number().min(-1).default(-1), // -1 for unlimited
  maxApplicationsPerJob: z.number().min(-1).default(-1),
  maxOrganizationMembers: z.number().min(-1).default(-1),
  maxAiInterviewsPerMonth: z.number().min(-1).default(-1),
  maxCandidatesPerMonth: z.number().min(-1).default(-1),

  // AI Features
  aiInterviewEnabled: z.boolean().default(false),
  aiAnalysisEnabled: z.boolean().default(false),
  aiRecommendationsEnabled: z.boolean().default(false),
  customAiQuestions: z.boolean().default(false),

  // Advanced features
  customBranding: z.boolean().default(false),
  advancedAnalytics: z.boolean().default(false),
  apiAccess: z.boolean().default(false),
  ssoIntegration: z.boolean().default(false),
  prioritySupport: z.boolean().default(false),
  dedicatedAccountManager: z.boolean().default(false),

  // Integration features
  atsIntegration: z.boolean().default(false),
  slackIntegration: z.boolean().default(false),
  teamsIntegration: z.boolean().default(false),
  webhooksEnabled: z.boolean().default(false),

  // Storage and export
  dataRetentionMonths: z.number().min(1).default(12),
  dataExportEnabled: z.boolean().default(false),
  bulkOperations: z.boolean().default(false),

  // Custom limits
  customLimits: z.array(FeatureLimitSchema).default([]),
});

export const PaymentProviderConfigSchema = z.object({
  provider: PaymentProviderSchema,
  productId: z.string().optional(), // Stripe product ID, Razorpay plan ID, etc.
  priceId: z.string().optional(), // Stripe price ID
  planId: z.string().optional(), // Provider-specific plan ID
  webhookEndpoint: z.string().url().optional(),
  isActive: z.boolean().default(true),
});

export const CreateSubscriptionPlanSchema = z.object({
  name: z.string().min(1, 'Plan name is required'),
  description: z.string().optional(),
  type: PlanTypeSchema,
  status: PlanStatusSchema.default('active'),
  pricing: z.array(PricingSchema).min(1, 'At least one pricing option is required'),
  features: PlanFeaturesSchema,
  paymentProviders: z.array(PaymentProviderConfigSchema).default([]),
  trialDays: z.number().min(0).default(0),
  setupFee: z.number().min(0).default(0),
  isPopular: z.boolean().default(false),
  sortOrder: z.number().default(0),
  metadata: z.record(z.unknown()).optional(),
});

export const UpdateSubscriptionPlanSchema = CreateSubscriptionPlanSchema.partial();

// TypeScript interfaces
export type PlanType = z.infer<typeof PlanTypeSchema>;
export type BillingInterval = z.infer<typeof BillingIntervalSchema>;
export type PlanStatus = z.infer<typeof PlanStatusSchema>;
export type PaymentProvider = z.infer<typeof PaymentProviderSchema>;
export type Pricing = z.infer<typeof PricingSchema>;
export type FeatureLimit = z.infer<typeof FeatureLimitSchema>;
export type PlanFeatures = z.infer<typeof PlanFeaturesSchema>;
export type PaymentProviderConfig = z.infer<typeof PaymentProviderConfigSchema>;
export type CreateSubscriptionPlanInput = z.infer<typeof CreateSubscriptionPlanSchema>;
export type UpdateSubscriptionPlanInput = z.infer<typeof UpdateSubscriptionPlanSchema>;

export interface ISubscriptionPlan extends Document {
  _id: any;
  planId: string;
  name: string;
  description?: string;
  type: PlanType;
  status: PlanStatus;
  pricing: Pricing[];
  features: PlanFeatures;
  paymentProviders: PaymentProviderConfig[];
  trialDays: number;
  setupFee: number;
  isPopular: boolean;
  sortOrder: number;
  metadata?: Record<string, unknown>;
  subscriberCount: number;
  revenueGenerated: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  getPricingForInterval(interval: BillingInterval): Pricing | null;
  getPricingForProvider(provider: PaymentProvider, interval: BillingInterval): Pricing | null;
  hasFeature(featureName: keyof PlanFeatures): boolean;
  getFeatureLimit(featureName: string): number;
  canUpgradeFrom(otherPlan: ISubscriptionPlan): boolean;
  canDowngradeFrom(otherPlan: ISubscriptionPlan): boolean;
  calculateProration(fromPlan: ISubscriptionPlan, daysRemaining: number): number;
  getDisplayPrice(interval?: BillingInterval): string;
  isFeatureEnabled(featureName: keyof PlanFeatures): boolean;
  getProviderConfig(provider: PaymentProvider): PaymentProviderConfig | null;
}

export interface ISubscriptionPlanModel extends Model<ISubscriptionPlan> {
  createPlan(planData: CreateSubscriptionPlanInput): Promise<ISubscriptionPlan>;
  findByPlanId(planId: string): Promise<ISubscriptionPlan | null>;
  findActivePlans(): Promise<ISubscriptionPlan[]>;
  findByType(type: PlanType): Promise<ISubscriptionPlan[]>;
  findByProvider(provider: PaymentProvider): Promise<ISubscriptionPlan[]>;
  getPublicPlans(): Promise<ISubscriptionPlan[]>;
  getPlanComparison(): Promise<any>;
  findUpgradePath(currentPlanId: string): Promise<ISubscriptionPlan[]>;
  findDowngradePath(currentPlanId: string): Promise<ISubscriptionPlan[]>;
}
