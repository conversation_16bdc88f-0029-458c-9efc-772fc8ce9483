import { Document as MongoDocument, Model } from 'mongoose';
import { z } from 'zod';

// Zod validation schemas
export const ApplicationStatusSchema = z.enum([
  'submitted',
  'under_review',
  'screening',
  'interview_scheduled',
  'interviewing',
  'technical_assessment',
  'final_review',
  'offer_extended',
  'offer_accepted',
  'offer_declined',
  'rejected',
  'withdrawn',
  'on_hold',
]);

export const ApplicationSourceSchema = z.enum([
  'direct',
  'job_board',
  'referral',
  'social_media',
  'company_website',
  'recruiter',
  'career_fair',
  'university',
  'other',
]);

export const InterviewStageSchema = z.object({
  name: z.string().min(1, 'Stage name is required'),
  type: z.enum(['phone', 'video', 'in_person', 'technical', 'ai_interview', 'assessment']),
  status: z.enum(['pending', 'scheduled', 'completed', 'cancelled', 'no_show']),
  scheduledAt: z.date().optional(),
  completedAt: z.date().optional(),
  duration: z.number().min(1).optional(),
  interviewerId: z.string().optional(),
  feedback: z.string().optional(),
  score: z.number().min(0).max(10).optional(),
  notes: z.string().optional(),
  aiInterviewId: z.string().optional(),
});

export const DocumentSchema = z.object({
  type: z.enum(['resume', 'cover_letter', 'portfolio', 'certificate', 'transcript', 'other']),
  name: z.string().min(1, 'Document name is required'),
  url: z.string().url('Invalid document URL'),
  size: z.number().min(0).optional(),
  mimeType: z.string().optional(),
  uploadedAt: z.date().default(() => new Date()),
});

export const CreateJobApplicationSchema = z.object({
  jobId: z.string().min(1, 'Job ID is required'),
  applicantId: z.string().min(1, 'Applicant ID is required'),
  coverLetter: z.string().optional(),
  expectedSalary: z
    .object({
      amount: z.number().min(0),
      currency: z.string().length(3).default('USD'),
      period: z.enum(['hourly', 'monthly', 'yearly']).default('yearly'),
    })
    .optional(),
  availabilityDate: z.date().optional(),
  source: ApplicationSourceSchema.default('direct'),
  referredBy: z.string().optional(),
  documents: z.array(DocumentSchema).default([]),
  questionsAndAnswers: z
    .array(
      z.object({
        question: z.string().min(1, 'Question is required'),
        answer: z.string().min(1, 'Answer is required'),
      })
    )
    .default([]),
  isAnonymous: z.boolean().default(false),
  metadata: z.record(z.unknown()).optional(),
});

export const UpdateJobApplicationSchema = z.object({
  status: ApplicationStatusSchema.optional(),
  coverLetter: z.string().optional(),
  expectedSalary: z
    .object({
      amount: z.number().min(0),
      currency: z.string().length(3).default('USD'),
      period: z.enum(['hourly', 'monthly', 'yearly']).default('yearly'),
    })
    .optional(),
  availabilityDate: z.date().optional(),
  documents: z.array(DocumentSchema).optional(),
  questionsAndAnswers: z
    .array(
      z.object({
        question: z.string().min(1, 'Question is required'),
        answer: z.string().min(1, 'Answer is required'),
      })
    )
    .optional(),
  notes: z.string().optional(),
  rating: z.number().min(1).max(5).optional(),
  tags: z.array(z.string()).optional(),
  rejectionReason: z.string().optional(),
  feedback: z.string().optional(),
});

// TypeScript interfaces
export type ApplicationStatus = z.infer<typeof ApplicationStatusSchema>;
export type ApplicationSource = z.infer<typeof ApplicationSourceSchema>;
export type InterviewStage = z.infer<typeof InterviewStageSchema>;
export type Document = z.infer<typeof DocumentSchema>;
export type CreateJobApplicationInput = z.infer<typeof CreateJobApplicationSchema>;
export type UpdateJobApplicationInput = z.infer<typeof UpdateJobApplicationSchema>;

export interface IJobApplication extends MongoDocument {
  _id: any;
  applicationId: string;
  jobId: string;
  applicantId: string;
  status: ApplicationStatus;
  coverLetter?: string;
  expectedSalary?: {
    amount: number;
    currency: string;
    period: string;
  };
  availabilityDate?: Date;
  source: ApplicationSource;
  referredBy?: string;
  documents: Document[];
  questionsAndAnswers: Array<{
    question: string;
    answer: string;
  }>;
  interviewStages: InterviewStage[];
  currentStage?: string;
  isAnonymous: boolean;
  notes?: string;
  rating?: number;
  tags: string[];
  rejectionReason?: string;
  feedback?: string;
  reviewedBy?: string;
  reviewedAt?: Date;
  withdrawnAt?: Date;
  withdrawalReason?: string;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  updateStatus(newStatus: ApplicationStatus, updatedBy?: string): Promise<void>;
  addInterviewStage(stage: InterviewStage): Promise<void>;
  updateInterviewStage(stageName: string, updates: Partial<InterviewStage>): Promise<void>;
  addDocument(document: Document): Promise<void>;
  removeDocument(documentName: string): Promise<void>;
  withdraw(reason?: string): Promise<void>;
  addNote(note: string, addedBy?: string): Promise<void>;
  addTag(tag: string): Promise<void>;
  removeTag(tag: string): Promise<void>;
  calculateScore(): number;
  getTimeline(): Array<{ event: string; timestamp: Date; details?: any }>;
  canWithdraw(): boolean;
  isInProgress(): boolean;
}

export interface IJobApplicationModel extends Model<IJobApplication> {
  createApplication(applicationData: CreateJobApplicationInput): Promise<IJobApplication>;
  findByApplicationId(applicationId: string): Promise<IJobApplication | null>;
  findByJob(jobId: string): Promise<IJobApplication[]>;
  findByApplicant(applicantId: string): Promise<IJobApplication[]>;
  findByStatus(status: ApplicationStatus): Promise<IJobApplication[]>;
  findByOrganization(organizationId: string): Promise<IJobApplication[]>;
  searchApplications(query: string): Promise<IJobApplication[]>;
  getApplicationStats(jobId?: string, organizationId?: string): Promise<any>;
  findDuplicateApplications(jobId: string, applicantId: string): Promise<IJobApplication[]>;
}
