import { Document, Model } from 'mongoose';
import { z } from 'zod';

// Zod validation schemas
export const JobTypeSchema = z.enum([
  'full_time',
  'part_time',
  'contract',
  'freelance',
  'internship',
  'temporary',
]);
export const JobStatusSchema = z.enum(['draft', 'published', 'paused', 'closed', 'archived']);
export const JobExperienceLevelSchema = z.enum([
  'entry',
  'junior',
  'mid',
  'senior',
  'lead',
  'executive',
]);
export const WorkLocationSchema = z.enum(['remote', 'hybrid', 'on_site']);
export const PrioritySchema = z.enum(['low', 'medium', 'high', 'urgent']);

export const SalaryRangeSchema = z.object({
  min: z.number().min(0),
  max: z.number().min(0),
  currency: z.string().length(3).default('USD'),
  period: z.enum(['hourly', 'daily', 'weekly', 'monthly', 'yearly']).default('yearly'),
  isNegotiable: z.boolean().default(false),
});

export const LocationSchema = z.object({
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().min(1, 'Country is required'),
  timezone: z.string().optional(),
  isRemoteAllowed: z.boolean().default(false),
});

export const RequirementsSchema = z.object({
  education: z.array(z.string()).default([]),
  experience: z.array(z.string()).default([]),
  skills: z.array(z.string()).default([]),
  certifications: z.array(z.string()).default([]),
  languages: z.array(z.string()).default([]),
  other: z.array(z.string()).default([]),
});

export const BenefitsSchema = z.object({
  healthInsurance: z.boolean().default(false),
  dentalInsurance: z.boolean().default(false),
  visionInsurance: z.boolean().default(false),
  retirementPlan: z.boolean().default(false),
  paidTimeOff: z.boolean().default(false),
  flexibleSchedule: z.boolean().default(false),
  remoteWork: z.boolean().default(false),
  professionalDevelopment: z.boolean().default(false),
  gymMembership: z.boolean().default(false),
  stockOptions: z.boolean().default(false),
  bonuses: z.boolean().default(false),
  other: z.array(z.string()).default([]),
});

export const InterviewProcessSchema = z.object({
  stages: z
    .array(
      z.object({
        name: z.string().min(1, 'Stage name is required'),
        description: z.string().optional(),
        duration: z.number().min(1).optional(),
        type: z.enum(['phone', 'video', 'in_person', 'technical', 'ai_interview', 'assessment']),
        isRequired: z.boolean().default(true),
        order: z.number().min(1),
      })
    )
    .default([]),
  estimatedDuration: z.string().optional(),
  interviewers: z.array(z.string()).default([]),
  notes: z.string().optional(),
});

export const CreateJobSchema = z.object({
  title: z.string().min(1, 'Job title is required'),
  description: z.string().min(1, 'Job description is required'),
  shortDescription: z.string().optional(),
  organizationId: z.string().min(1, 'Organization ID is required'),
  department: z.string().optional(),
  type: JobTypeSchema,
  experienceLevel: JobExperienceLevelSchema,
  workLocation: WorkLocationSchema,
  location: LocationSchema,
  salaryRange: SalaryRangeSchema.optional(),
  requirements: RequirementsSchema.optional(),
  benefits: BenefitsSchema.optional(),
  interviewProcess: InterviewProcessSchema.optional(),
  priority: PrioritySchema.default('medium'),
  applicationDeadline: z.date().optional(),
  startDate: z.date().optional(),
  isUrgent: z.boolean().default(false),
  isConfidential: z.boolean().default(false),
  allowAnonymousApplications: z.boolean().default(true),
  requireCoverLetter: z.boolean().default(false),
  enableAiScreening: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  createdBy: z.string().min(1, 'Creator ID is required'),
});

export const UpdateJobSchema = CreateJobSchema.partial().omit({
  organizationId: true,
  createdBy: true,
});

// TypeScript interfaces
export type JobType = z.infer<typeof JobTypeSchema>;
export type JobStatus = z.infer<typeof JobStatusSchema>;
export type JobExperienceLevel = z.infer<typeof JobExperienceLevelSchema>;
export type WorkLocation = z.infer<typeof WorkLocationSchema>;
export type Priority = z.infer<typeof PrioritySchema>;
export type SalaryRange = z.infer<typeof SalaryRangeSchema>;
export type Location = z.infer<typeof LocationSchema>;
export type Requirements = z.infer<typeof RequirementsSchema>;
export type Benefits = z.infer<typeof BenefitsSchema>;
export type InterviewProcess = z.infer<typeof InterviewProcessSchema>;
export type CreateJobInput = z.infer<typeof CreateJobSchema>;
export type UpdateJobInput = z.infer<typeof UpdateJobSchema>;

export interface IJob extends Document {
  _id: any;
  jobId: string;
  title: string;
  description: string;
  shortDescription?: string;
  organizationId: string;
  department?: string;
  type: JobType;
  status: JobStatus;
  experienceLevel: JobExperienceLevel;
  workLocation: WorkLocation;
  location: Location;
  salaryRange?: SalaryRange;
  requirements?: Requirements;
  benefits?: Benefits;
  interviewProcess?: InterviewProcess;
  priority: Priority;
  applicationDeadline?: Date;
  startDate?: Date;
  isUrgent: boolean;
  isConfidential: boolean;
  allowAnonymousApplications: boolean;
  requireCoverLetter: boolean;
  enableAiScreening: boolean;
  tags: string[];
  applicationCount: number;
  viewCount: number;
  publishedAt?: Date;
  closedAt?: Date;
  archivedAt?: Date;
  createdBy: string;
  lastModifiedBy?: string;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  publish(): Promise<void>;
  pause(): Promise<void>;
  close(): Promise<void>;
  archive(): Promise<void>;
  incrementViewCount(): Promise<void>;
  updateApplicationCount(): Promise<number>;
  isActive(): boolean;
  isExpired(): boolean;
  canApply(): boolean;
  getPublicView(): Partial<IJob>;
  addTag(tag: string): Promise<void>;
  removeTag(tag: string): Promise<void>;
}

export interface IJobModel extends Model<IJob> {
  createJob(jobData: CreateJobInput): Promise<IJob>;
  findByJobId(jobId: string): Promise<IJob | null>;
  findByOrganization(organizationId: string): Promise<IJob[]>;
  findActiveJobs(): Promise<IJob[]>;
  findByStatus(status: JobStatus): Promise<IJob[]>;
  searchJobs(query: string): Promise<IJob[]>;
  findByLocation(location: string): Promise<IJob[]>;
  findByType(type: JobType): Promise<IJob[]>;
  findByExperienceLevel(level: JobExperienceLevel): Promise<IJob[]>;
  findExpiredJobs(): Promise<IJob[]>;
  getJobAnalytics(organizationId?: string): Promise<any>;
}
