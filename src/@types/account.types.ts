import { Document, Model } from 'mongoose';
import { z } from 'zod';

// Zod validation schemas
export const GenderSchema = z.enum(['male', 'female', 'non_binary', 'prefer_not_to_say']);
export const ExperienceLevelSchema = z.enum([
  'entry',
  'junior',
  'mid',
  'senior',
  'lead',
  'executive',
]);
export const EmploymentStatusSchema = z.enum([
  'employed',
  'unemployed',
  'student',
  'freelancer',
  'consultant',
  'retired',
]);

export const EducationSchema = z.object({
  institution: z.string().min(1, 'Institution is required'),
  degree: z.string().min(1, 'Degree is required'),
  fieldOfStudy: z.string().min(1, 'Field of study is required'),
  startDate: z.date(),
  endDate: z.date().optional(),
  isCurrentlyStudying: z.boolean().default(false),
  grade: z.string().optional(),
  description: z.string().optional(),
});

export const ExperienceSchema = z.object({
  company: z.string().min(1, 'Company is required'),
  position: z.string().min(1, 'Position is required'),
  startDate: z.date(),
  endDate: z.date().optional(),
  isCurrentPosition: z.boolean().default(false),
  description: z.string().optional(),
  skills: z.array(z.string()).default([]),
  achievements: z.array(z.string()).default([]),
});

export const SkillSchema = z.object({
  name: z.string().min(1, 'Skill name is required'),
  level: z.enum(['beginner', 'intermediate', 'advanced', 'expert']),
  yearsOfExperience: z.number().min(0).optional(),
  isVerified: z.boolean().default(false),
  verifiedBy: z.string().optional(),
  verifiedAt: z.date().optional(),
});

export const CertificationSchema = z.object({
  name: z.string().min(1, 'Certification name is required'),
  issuer: z.string().min(1, 'Issuer is required'),
  issueDate: z.date(),
  expiryDate: z.date().optional(),
  credentialId: z.string().optional(),
  credentialUrl: z.string().url().optional(),
  isVerified: z.boolean().default(false),
});

export const LanguageSchema = z.object({
  language: z.string().min(1, 'Language is required'),
  proficiency: z.enum(['basic', 'conversational', 'fluent', 'native']),
  isNative: z.boolean().default(false),
});

export const SocialProfileSchema = z.object({
  platform: z.string().min(1, 'Platform is required'),
  username: z.string().min(1, 'Username is required'),
  url: z.string().url('Invalid URL'),
  isPublic: z.boolean().default(true),
});

export const PreferencesSchema = z.object({
  jobTypes: z
    .array(z.enum(['full_time', 'part_time', 'contract', 'freelance', 'internship']))
    .default([]),
  workLocations: z.array(z.enum(['remote', 'hybrid', 'on_site'])).default([]),
  salaryExpectation: z
    .object({
      min: z.number().min(0).optional(),
      max: z.number().min(0).optional(),
      currency: z.string().length(3).default('USD'),
      period: z.enum(['hourly', 'monthly', 'yearly']).default('yearly'),
    })
    .optional(),
  preferredIndustries: z.array(z.string()).default([]),
  willingToRelocate: z.boolean().default(false),
  availabilityDate: z.date().optional(),
  noticePeriod: z.string().optional(),
});

export const CreateAccountSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  bio: z.string().optional(),
  headline: z.string().optional(),
  location: z.string().optional(),
  website: z.string().url().optional(),
  dateOfBirth: z.date().optional(),
  gender: GenderSchema.optional(),
  nationality: z.string().optional(),
  experienceLevel: ExperienceLevelSchema.optional(),
  employmentStatus: EmploymentStatusSchema.optional(),
  education: z.array(EducationSchema).default([]),
  experience: z.array(ExperienceSchema).default([]),
  skills: z.array(SkillSchema).default([]),
  certifications: z.array(CertificationSchema).default([]),
  languages: z.array(LanguageSchema).default([]),
  socialProfiles: z.array(SocialProfileSchema).default([]),
  preferences: PreferencesSchema.optional(),
  resume: z.string().url().optional(),
  portfolio: z.string().url().optional(),
  coverLetter: z.string().optional(),
  isPublic: z.boolean().default(true),
  isSearchable: z.boolean().default(true),
});

export const UpdateAccountSchema = CreateAccountSchema.partial().omit({ userId: true });

// TypeScript interfaces
export type Gender = z.infer<typeof GenderSchema>;
export type ExperienceLevel = z.infer<typeof ExperienceLevelSchema>;
export type EmploymentStatus = z.infer<typeof EmploymentStatusSchema>;
export type Education = z.infer<typeof EducationSchema>;
export type Experience = z.infer<typeof ExperienceSchema>;
export type Skill = z.infer<typeof SkillSchema>;
export type Certification = z.infer<typeof CertificationSchema>;
export type Language = z.infer<typeof LanguageSchema>;
export type SocialProfile = z.infer<typeof SocialProfileSchema>;
export type Preferences = z.infer<typeof PreferencesSchema>;
export type CreateAccountInput = z.infer<typeof CreateAccountSchema>;
export type UpdateAccountInput = z.infer<typeof UpdateAccountSchema>;

export interface IAccount extends Document {
  _id: any;
  accountId: string;
  userId: string;
  bio?: string;
  headline?: string;
  location?: string;
  website?: string;
  dateOfBirth?: Date;
  gender?: Gender;
  nationality?: string;
  experienceLevel?: ExperienceLevel;
  employmentStatus?: EmploymentStatus;
  education: Education[];
  experience: Experience[];
  skills: Skill[];
  certifications: Certification[];
  languages: Language[];
  socialProfiles: SocialProfile[];
  preferences?: Preferences;
  resume?: string;
  portfolio?: string;
  coverLetter?: string;
  isPublic: boolean;
  isSearchable: boolean;
  profileViews: number;
  profileCompleteness: number;
  lastProfileUpdate: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  calculateProfileCompleteness(): number;
  addEducation(education: Education): Promise<void>;
  removeEducation(educationId: string): Promise<void>;
  addExperience(experience: Experience): Promise<void>;
  removeExperience(experienceId: string): Promise<void>;
  addSkill(skill: Skill): Promise<void>;
  removeSkill(skillName: string): Promise<void>;
  getPublicProfile(): Partial<IAccount>;
  incrementProfileViews(): Promise<void>;
  updateProfileCompleteness(): Promise<void>;
}

export interface IAccountModel extends Model<IAccount> {
  createAccount(accountData: CreateAccountInput): Promise<IAccount>;
  findByUserId(userId: string): Promise<IAccount | null>;
  findByAccountId(accountId: string): Promise<IAccount | null>;
  searchAccounts(query: string): Promise<IAccount[]>;
  findBySkills(skills: string[]): Promise<IAccount[]>;
  findByExperienceLevel(level: ExperienceLevel): Promise<IAccount[]>;
  findByLocation(location: string): Promise<IAccount[]>;
  getPublicAccounts(): Promise<IAccount[]>;
}
