import { Document, Model } from 'mongoose';
import { z } from 'zod';

// Zod validation schemas
export const InterviewStatusSchema = z.enum([
  'scheduled',
  'in_progress',
  'completed',
  'cancelled',
  'expired',
  'failed',
]);
export const InterviewTypeSchema = z.enum([
  'screening',
  'technical',
  'behavioral',
  'cultural_fit',
  'custom',
]);
export const QuestionTypeSchema = z.enum([
  'multiple_choice',
  'text',
  'code',
  'video',
  'audio',
  'file_upload',
]);

export const QuestionSchema = z.object({
  id: z.string().min(1, 'Question ID is required'),
  type: QuestionTypeSchema,
  category: z.string().min(1, 'Category is required'),
  question: z.string().min(1, 'Question text is required'),
  options: z.array(z.string()).optional(),
  correctAnswer: z.string().optional(),
  timeLimit: z.number().min(1).optional(),
  points: z.number().min(0).default(1),
  difficulty: z.enum(['easy', 'medium', 'hard']).default('medium'),
  tags: z.array(z.string()).default([]),
  isRequired: z.boolean().default(true),
});

export const ResponseSchema = z.object({
  questionId: z.string().min(1, 'Question ID is required'),
  answer: z.string().optional(),
  selectedOptions: z.array(z.string()).optional(),
  fileUrl: z.string().url().optional(),
  audioUrl: z.string().url().optional(),
  videoUrl: z.string().url().optional(),
  codeSubmission: z
    .object({
      code: z.string(),
      language: z.string(),
      executionTime: z.number().optional(),
      testResults: z
        .array(
          z.object({
            input: z.string(),
            expectedOutput: z.string(),
            actualOutput: z.string(),
            passed: z.boolean(),
          })
        )
        .optional(),
    })
    .optional(),
  timeSpent: z.number().min(0).default(0),
  isCorrect: z.boolean().optional(),
  score: z.number().min(0).optional(),
  aiAnalysis: z
    .object({
      sentiment: z.string().optional(),
      confidence: z.number().min(0).max(1).optional(),
      keywords: z.array(z.string()).optional(),
      relevance: z.number().min(0).max(1).optional(),
    })
    .optional(),
  submittedAt: z.date().default(() => new Date()),
});

export const AnalysisMetricsSchema = z.object({
  totalScore: z.number().min(0).max(100),
  categoryScores: z.record(z.number().min(0).max(100)),
  timeEfficiency: z.number().min(0).max(1),
  completionRate: z.number().min(0).max(1),
  accuracyRate: z.number().min(0).max(1),
  consistencyScore: z.number().min(0).max(1),
  communicationScore: z.number().min(0).max(100).optional(),
  technicalScore: z.number().min(0).max(100).optional(),
  problemSolvingScore: z.number().min(0).max(100).optional(),
});

export const SkillAssessmentSchema = z.object({
  skill: z.string().min(1, 'Skill name is required'),
  level: z.enum(['beginner', 'intermediate', 'advanced', 'expert']),
  score: z.number().min(0).max(100),
  confidence: z.number().min(0).max(1),
  evidence: z.array(z.string()).default([]),
  recommendations: z.array(z.string()).default([]),
});

export const PersonalityTraitsSchema = z.object({
  openness: z.number().min(0).max(100).optional(),
  conscientiousness: z.number().min(0).max(100).optional(),
  extraversion: z.number().min(0).max(100).optional(),
  agreeableness: z.number().min(0).max(100).optional(),
  neuroticism: z.number().min(0).max(100).optional(),
  leadership: z.number().min(0).max(100).optional(),
  teamwork: z.number().min(0).max(100).optional(),
  adaptability: z.number().min(0).max(100).optional(),
  creativity: z.number().min(0).max(100).optional(),
  analyticalThinking: z.number().min(0).max(100).optional(),
});

export const RecommendationSchema = z.object({
  type: z.enum(['hire', 'maybe', 'no_hire', 'needs_more_assessment']),
  confidence: z.number().min(0).max(1),
  reasoning: z.string().min(1, 'Reasoning is required'),
  strengths: z.array(z.string()).default([]),
  weaknesses: z.array(z.string()).default([]),
  suggestions: z.array(z.string()).default([]),
  nextSteps: z.array(z.string()).default([]),
});

export const CreateAiInterviewSchema = z.object({
  jobApplicationId: z.string().min(1, 'Job application ID is required'),
  jobId: z.string().min(1, 'Job ID is required'),
  candidateId: z.string().min(1, 'Candidate ID is required'),
  organizationId: z.string().min(1, 'Organization ID is required'),
  type: InterviewTypeSchema,
  title: z.string().min(1, 'Interview title is required'),
  description: z.string().optional(),
  questions: z.array(QuestionSchema).min(1, 'At least one question is required'),
  timeLimit: z.number().min(1).optional(),
  passingScore: z.number().min(0).max(100).default(60),
  scheduledAt: z.date().optional(),
  expiresAt: z.date().optional(),
  settings: z
    .object({
      allowPause: z.boolean().default(true),
      allowReview: z.boolean().default(false),
      showResults: z.boolean().default(false),
      recordVideo: z.boolean().default(false),
      recordAudio: z.boolean().default(false),
      enableProctoring: z.boolean().default(false),
      randomizeQuestions: z.boolean().default(false),
      maxAttempts: z.number().min(1).default(1),
    })
    .default({}),
  createdBy: z.string().min(1, 'Creator ID is required'),
});

export const UpdateAiInterviewSchema = CreateAiInterviewSchema.partial().omit({
  jobApplicationId: true,
  jobId: true,
  candidateId: true,
  organizationId: true,
  createdBy: true,
});

// TypeScript interfaces
export type InterviewStatus = z.infer<typeof InterviewStatusSchema>;
export type InterviewType = z.infer<typeof InterviewTypeSchema>;
export type QuestionType = z.infer<typeof QuestionTypeSchema>;
export type Question = z.infer<typeof QuestionSchema>;
export type Response = z.infer<typeof ResponseSchema>;
export type AnalysisMetrics = z.infer<typeof AnalysisMetricsSchema>;
export type SkillAssessment = z.infer<typeof SkillAssessmentSchema>;
export type PersonalityTraits = z.infer<typeof PersonalityTraitsSchema>;
export type Recommendation = z.infer<typeof RecommendationSchema>;
export type CreateAiInterviewInput = z.infer<typeof CreateAiInterviewSchema>;
export type UpdateAiInterviewInput = z.infer<typeof UpdateAiInterviewSchema>;

export interface IAiInterview extends Document {
  _id: any;
  interviewId: string;
  jobApplicationId: string;
  jobId: string;
  candidateId: string;
  organizationId: string;
  type: InterviewType;
  status: InterviewStatus;
  title: string;
  description?: string;
  questions: Question[];
  responses: Response[];
  timeLimit?: number;
  passingScore: number;
  actualScore?: number;
  scheduledAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  expiresAt?: Date;
  timeSpent: number;
  analysisMetrics?: AnalysisMetrics;
  skillAssessments: SkillAssessment[];
  personalityTraits?: PersonalityTraits;
  recommendation?: Recommendation;
  settings: {
    allowPause: boolean;
    allowReview: boolean;
    showResults: boolean;
    recordVideo: boolean;
    recordAudio: boolean;
    enableProctoring: boolean;
    randomizeQuestions: boolean;
    maxAttempts: number;
  };
  attemptCount: number;
  videoRecordingUrl?: string;
  audioRecordingUrl?: string;
  proctoringSummary?: Record<string, unknown>;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  start(): Promise<void>;
  complete(): Promise<void>;
  cancel(): Promise<void>;
  addResponse(response: Response): Promise<void>;
  calculateScore(): Promise<number>;
  generateAnalysis(): Promise<void>;
  generateRecommendation(): Promise<void>;
  isExpired(): boolean;
  canStart(): boolean;
  canComplete(): boolean;
  getProgress(): { completed: number; total: number; percentage: number };
  getTimeRemaining(): number;
  exportResults(): any;
}

export interface IAiInterviewModel extends Model<IAiInterview> {
  createInterview(interviewData: CreateAiInterviewInput): Promise<IAiInterview>;
  findByInterviewId(interviewId: string): Promise<IAiInterview | null>;
  findByJobApplication(jobApplicationId: string): Promise<IAiInterview[]>;
  findByCandidate(candidateId: string): Promise<IAiInterview[]>;
  findByOrganization(organizationId: string): Promise<IAiInterview[]>;
  findByStatus(status: InterviewStatus): Promise<IAiInterview[]>;
  findExpiredInterviews(): Promise<IAiInterview[]>;
  getInterviewAnalytics(organizationId?: string): Promise<any>;
  findScheduledInterviews(): Promise<IAiInterview[]>;
}
