import { Document, Model } from 'mongoose';
import { z } from 'zod';

// Zod validation schemas
export const SubscriptionEventTypeSchema = z.enum([
  'subscription_created',
  'subscription_updated',
  'subscription_cancelled',
  'subscription_reactivated',
  'subscription_expired',
  'subscription_paused',
  'subscription_resumed',
  'plan_changed',
  'plan_upgraded',
  'plan_downgraded',
  'payment_succeeded',
  'payment_failed',
  'payment_pending',
  'payment_refunded',
  'payment_disputed',
  'invoice_created',
  'invoice_paid',
  'invoice_failed',
  'trial_started',
  'trial_ended',
  'trial_extended',
  'discount_applied',
  'discount_removed',
  'webhook_received',
  'manual_adjustment',
]);

export const PaymentStatusSchema = z.enum([
  'pending',
  'processing',
  'succeeded',
  'failed',
  'cancelled',
  'refunded',
  'disputed',
  'partially_refunded',
]);

export const PaymentMethodSchema = z.enum([
  'card',
  'bank_transfer',
  'wallet',
  'upi',
  'net_banking',
  'emi',
  'other',
]);

export const PaymentDetailsSchema = z.object({
  amount: z.number().min(0),
  currency: z.string().length(3).default('USD'),
  paymentMethod: PaymentMethodSchema.optional(),
  paymentMethodDetails: z.record(z.unknown()).optional(),
  transactionId: z.string().optional(),
  invoiceId: z.string().optional(),
  receiptUrl: z.string().url().optional(),
  refundAmount: z.number().min(0).optional(),
  refundReason: z.string().optional(),
  failureReason: z.string().optional(),
  processingFee: z.number().min(0).optional(),
  netAmount: z.number().min(0).optional(),
});

export const SubscriptionDetailsSchema = z.object({
  subscriptionId: z.string(),
  organizationId: z.string(),
  userId: z.string(),
  planId: z.string(),
  previousPlanId: z.string().optional(),
  status: z.string(),
  previousStatus: z.string().optional(),
  billingInterval: z.enum(['monthly', 'quarterly', 'yearly', 'lifetime']),
  currentPeriodStart: z.date(),
  currentPeriodEnd: z.date(),
  trialStart: z.date().optional(),
  trialEnd: z.date().optional(),
  cancelledAt: z.date().optional(),
  cancelReason: z.string().optional(),
});

export const CreateSubscriptionLogSchema = z.object({
  eventType: SubscriptionEventTypeSchema,
  subscriptionDetails: SubscriptionDetailsSchema,
  paymentDetails: PaymentDetailsSchema.optional(),
  paymentProvider: z.enum(['stripe', 'razorpay', 'polar']),
  providerEventId: z.string().optional(),
  providerSubscriptionId: z.string().optional(),
  providerCustomerId: z.string().optional(),
  webhookData: z.record(z.unknown()).optional(),
  metadata: z.record(z.unknown()).optional(),
  notes: z.string().optional(),
  processedBy: z.string().optional(), // User ID who processed this manually
  isManual: z.boolean().default(false),
});

// TypeScript interfaces
export type SubscriptionEventType = z.infer<typeof SubscriptionEventTypeSchema>;
export type PaymentStatus = z.infer<typeof PaymentStatusSchema>;
export type PaymentMethod = z.infer<typeof PaymentMethodSchema>;
export type PaymentDetails = z.infer<typeof PaymentDetailsSchema>;
export type SubscriptionDetails = z.infer<typeof SubscriptionDetailsSchema>;
export type CreateSubscriptionLogInput = z.infer<typeof CreateSubscriptionLogSchema>;

export interface ISubscriptionLog extends Document {
  _id: any;
  logId: string;
  eventType: SubscriptionEventType;
  subscriptionDetails: SubscriptionDetails;
  paymentDetails?: PaymentDetails;
  paymentProvider: 'stripe' | 'razorpay' | 'polar';
  providerEventId?: string;
  providerSubscriptionId?: string;
  providerCustomerId?: string;
  webhookData?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
  notes?: string;
  processedBy?: string;
  isManual: boolean;
  processedAt: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  isPaymentEvent(): boolean;
  isSubscriptionEvent(): boolean;
  getEventSummary(): string;
  getAmountFormatted(): string;
  isSuccessfulPayment(): boolean;
  isFailedPayment(): boolean;
  getRelatedLogs(): Promise<ISubscriptionLog[]>;
}

export interface ISubscriptionLogModel extends Model<ISubscriptionLog> {
  createLog(logData: CreateSubscriptionLogInput): Promise<ISubscriptionLog>;
  findBySubscription(subscriptionId: string): Promise<ISubscriptionLog[]>;
  findByOrganization(organizationId: string): Promise<ISubscriptionLog[]>;
  findByUser(userId: string): Promise<ISubscriptionLog[]>;
  findByEventType(eventType: SubscriptionEventType): Promise<ISubscriptionLog[]>;
  findByProvider(provider: 'stripe' | 'razorpay' | 'polar'): Promise<ISubscriptionLog[]>;
  findPaymentLogs(organizationId?: string): Promise<ISubscriptionLog[]>;
  findFailedPayments(organizationId?: string): Promise<ISubscriptionLog[]>;
  getRevenueStats(startDate: Date, endDate: Date, organizationId?: string): Promise<any>;
  getSubscriptionStats(organizationId?: string): Promise<any>;
  findByProviderEventId(providerEventId: string): Promise<ISubscriptionLog | null>;
}
