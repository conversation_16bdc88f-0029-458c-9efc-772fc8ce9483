// User Management Types
export * from './account.types';
export * from './otp.types';
export * from './user.types';

// Organization Management Types
export * from './organization.types';
export * from './organization-member.types';

// Job Management Types
export * from './job.types';
export * from './job-application.types';

// AI Interview System Types
export * from './ai-interview.types';

// Subscription Management Types
export * from './subscription-log.types';
export * from './subscription-plan.types';

// Activity Tracking Types
export * from './activity-log.types';

// Common utility types
export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface SearchOptions extends PaginationOptions {
  query?: string;
  filters?: Record<string, any>;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Array<{ field: string; message: string }>;
}

export interface DatabaseStats {
  database: {
    name: string;
    collections: number;
    documents: number;
    dataSize: number;
    storageSize: number;
    indexSize: number;
  };
  collections: Array<{
    name: string;
    documents: number;
    size: number;
    avgObjSize: number;
    indexes: number;
    indexSizes: number;
  }>;
}

export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy';
  details: {
    readyState: number;
    host?: string;
    name?: string;
    collections?: number;
    documents?: number;
    error?: string;
  };
}
