import { Document, Model } from 'mongoose';
import { z } from 'zod';

// Zod validation schemas
export const UserRoleSchema = z.enum(['admin', 'recruiter', 'candidate', 'interviewer']);
export const UserStatusSchema = z.enum(['active', 'inactive', 'pending', 'suspended']);

export const CreateUserSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters').optional(),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  role: UserRoleSchema.default('candidate'),
  phone: z.string().optional(),
  avatar: z.string().url().optional(),
  timezone: z.string().default('UTC'),
  isEmailVerified: z.boolean().default(false),
  emailVerifiedAt: z.date().optional(),
  acceptedTerms: z.boolean().default(true),
  marketingConsent: z.boolean().default(false),
  status: UserStatusSchema.default('pending'),
  lastLoginAt: z.date().optional(),
  oauthProviders: z
    .object({
      google: z
        .object({
          id: z.string(),
          email: z.string(),
          connectedAt: z.date(),
        })
        .optional(),
      microsoft: z
        .object({
          id: z.string(),
          email: z.string(),
          connectedAt: z.date(),
        })
        .optional(),
    })
    .optional(),
});

export const UpdateUserSchema = CreateUserSchema.partial().omit({ password: true });

export const LoginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().default(false),
});

export const ChangePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: z.string().min(8, 'New password must be at least 8 characters'),
    confirmPassword: z.string().min(1, 'Password confirmation is required'),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

// TypeScript interfaces
export type UserRole = z.infer<typeof UserRoleSchema>;
export type UserStatus = z.infer<typeof UserStatusSchema>;
export type CreateUserInput = z.infer<typeof CreateUserSchema>;
export type UpdateUserInput = z.infer<typeof UpdateUserSchema>;
export type LoginInput = z.infer<typeof LoginSchema>;
export type ChangePasswordInput = z.infer<typeof ChangePasswordSchema>;

export interface IUser extends Document {
  _id: any;
  userId: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  status: UserStatus;
  phone?: string;
  avatar?: string;
  timezone: string;
  isEmailVerified: boolean;
  emailVerifiedAt?: Date;
  lastLoginAt?: Date;
  lastActiveAt?: Date;
  loginAttempts: number;
  lockUntil?: Date;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  acceptedTerms: boolean;
  acceptedTermsAt?: Date;
  marketingConsent: boolean;
  twoFactorEnabled: boolean;
  twoFactorSecret?: string;
  oauthProviders?: {
    google?: {
      id: string;
      email: string;
      connectedAt: Date;
    };
    microsoft?: {
      id: string;
      email: string;
      connectedAt: Date;
    };
  };
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  comparePassword(candidatePassword: string): Promise<boolean>;
  generatePasswordResetToken(): string;
  generateEmailVerificationToken(): string;
  incrementLoginAttempts(): Promise<void>;
  resetLoginAttempts(): Promise<void>;
  isLocked(): boolean;
  getPublicProfile(): Partial<IUser>;
  updateLastActive(): Promise<void>;
  enable2FA(): Promise<string>;
  disable2FA(): Promise<void>;
  verify2FA(token: string): boolean;
}

export interface IUserModel extends Model<IUser> {
  createUser(userData: CreateUserInput): Promise<IUser>;
  findByEmail(email: string): Promise<IUser | null>;
  findByUserId(userId: string): Promise<IUser | null>;
  findByResetToken(token: string): Promise<IUser | null>;
  authenticate(email: string, password: string): Promise<{ user: IUser | null; error?: string }>;
  findActiveUsers(): Promise<IUser[]>;
  findByRole(role: UserRole): Promise<IUser[]>;
  searchUsers(query: string): Promise<IUser[]>;
}
