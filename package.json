{"name": "hirelytics", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 80 --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "check-types": "tsc --noEmit", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md,css,scss}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md,css,scss}\"", "lint-staged": "lint-staged --allow-empty", "prepare": "husky"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@hookform/resolvers": "^5.1.1", "@oslojs/crypto": "^1.0.1", "@oslojs/otp": "^1.1.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.81.0", "@tanstack/react-query-devtools": "^5.81.0", "@trpc/client": "^11.4.2", "@trpc/react-query": "^11.4.2", "@trpc/server": "^11.4.2", "ai": "^4.3.16", "arctic": "^2.1.0", "argon2": "^0.43.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.18.1", "generate-password": "^1.7.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.522.0", "mongodb": "^6.17.0", "mongoose": "^8.16.0", "nanoid": "^5.1.5", "next": "15.3.4", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "nodemailer": "^6.10.1", "nuqs": "^2.4.3", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.4", "sonner": "^2.0.5", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3", "@next/eslint-plugin-next": "^15.3.3", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}